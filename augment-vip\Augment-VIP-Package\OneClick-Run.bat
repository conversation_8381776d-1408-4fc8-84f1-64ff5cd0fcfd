@echo off
title Augment VIP - One Click Tool
color 0A

echo.
echo ========================================
echo     Augment VIP - One Click Tool
echo ========================================
echo.
echo Checking environment...

:: Check PowerShell
powershell -Command "Write-Host 'PowerShell Available' -ForegroundColor Green"
if errorlevel 1 (
    echo PowerShell not available, please install PowerShell
    pause
    exit /b 1
)

:: Check script files
if not exist "scripts\clean_code_db.ps1" (
    echo Script files not found, please ensure you are in the correct directory
    pause
    exit /b 1
)

echo Environment check complete
echo.

:MENU
echo ========================================
echo           Select Operation
echo ========================================
echo.
echo [1] Full Installation and Run All Features (Recommended)
echo [2] Clean VS Code Database Only
echo [3] Modify Telemetry IDs Only
echo [4] Show Help Information
echo [5] Exit
echo.
set /p choice=Please enter your choice (1-5): 

if "%choice%"=="1" goto FULL_INSTALL
if "%choice%"=="2" goto CLEAN_DB
if "%choice%"=="3" goto MODIFY_ID
if "%choice%"=="4" goto SHOW_HELP
if "%choice%"=="5" goto EXIT
echo Invalid choice, please try again
goto MENU

:FULL_INSTALL
echo.
echo ========================================
echo    Running Full Installation and Clean
echo ========================================
echo.
echo Running full installation...
powershell -ExecutionPolicy Bypass -File ".\install.ps1" -All
echo.
echo Operation completed!
pause
goto MENU

:CLEAN_DB
echo.
echo ========================================
echo        Cleaning VS Code Database
echo ========================================
echo.
echo Cleaning database...
powershell -ExecutionPolicy Bypass -File ".\scripts\clean_code_db.ps1"
echo.
echo Operation completed!
pause
goto MENU

:MODIFY_ID
echo.
echo ========================================
echo         Modifying Telemetry IDs
echo ========================================
echo.
echo Modifying telemetry IDs...
powershell -ExecutionPolicy Bypass -File ".\scripts\id_modifier.ps1"
echo.
echo Operation completed!
pause
goto MENU

:SHOW_HELP
echo.
echo ========================================
echo           Help Information
echo ========================================
echo.
echo Augment VIP Tool Description:
echo.
echo 1. Database Cleaning Feature:
echo    - Removes Augment-related entries from VS Code database
echo    - Automatically creates backup files
echo    - Supports VS Code and VS Code Insiders
echo.
echo 2. Telemetry ID Modification Feature:
echo    - Generates random machine ID and device ID
echo    - Modifies VS Code telemetry identifiers
echo    - Automatically creates configuration file backups
echo.
echo Important Notes:
echo - Please close VS Code before use
echo - Restart VS Code after modifications to take effect
echo - All operations automatically create backup files
echo - This tool is for educational and research purposes only
echo.
pause
goto MENU

:EXIT
echo.
echo Thank you for using Augment VIP Tool!
echo.
pause
exit /b 0
