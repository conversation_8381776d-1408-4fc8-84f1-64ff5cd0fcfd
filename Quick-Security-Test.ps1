# Quick Security Test for Augment VIP Tool
Write-Host "🔍 AUGMENT VIP TOOL - QUICK SECURITY TEST" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

$targetFile = "augment-vip\AugmentVIP-Secure-NoEmoji.exe"

if (-not (Test-Path $targetFile)) {
    Write-Host "❌ File not found: $targetFile" -ForegroundColor Red
    exit
}

Write-Host "🎯 Testing: $targetFile" -ForegroundColor Green
Write-Host ""

# 1. 基本文件信息
Write-Host "📋 1. FILE INFORMATION" -ForegroundColor Yellow
$file = Get-Item $targetFile
Write-Host "Size: $($file.Length) bytes"
Write-Host "Created: $($file.CreationTime)"
Write-Host "Modified: $($file.LastWriteTime)"

# 2. 文件哈希
Write-Host ""
Write-Host "🔐 2. FILE HASH" -ForegroundColor Yellow
$hash = Get-FileHash $targetFile -Algorithm SHA256
Write-Host "SHA256: $($hash.Hash)"

# 3. 数字签名检查
Write-Host ""
Write-Host "✍️  3. DIGITAL SIGNATURE" -ForegroundColor Yellow
try {
    $signature = Get-AuthenticodeSignature $targetFile
    Write-Host "Status: $($signature.Status)"
    if ($signature.SignerCertificate) {
        Write-Host "Signer: $($signature.SignerCertificate.Subject)"
    } else {
        Write-Host "Signer: Not signed (self-compiled)"
    }
} catch {
    Write-Host "Status: Unable to verify"
}

# 4. 字符串分析
Write-Host ""
Write-Host "🔤 4. STRING ANALYSIS" -ForegroundColor Yellow
try {
    $content = Get-Content $targetFile -Raw -Encoding Byte
    $text = [System.Text.Encoding]::ASCII.GetString($content)
    
    # 检查敏感关键词
    $keywords = @("password", "secret", "admin", "root", "hack", "crack")
    $found = @()
    foreach ($keyword in $keywords) {
        if ($text -match $keyword) {
            $found += $keyword
        }
    }
    
    if ($found.Count -gt 0) {
        Write-Host "⚠️  Sensitive keywords found: $($found -join ', ')" -ForegroundColor Yellow
    } else {
        Write-Host "✅ No sensitive keywords detected" -ForegroundColor Green
    }
    
    # 检查网络活动
    $networkKeywords = @("http", "tcp", "socket", "connect", "download")
    $networkFound = @()
    foreach ($keyword in $networkKeywords) {
        if ($text -match $keyword) {
            $networkFound += $keyword
        }
    }
    
    if ($networkFound.Count -gt 0) {
        Write-Host "🌐 Network-related strings: $($networkFound -join ', ')" -ForegroundColor Cyan
    } else {
        Write-Host "✅ No network activity strings detected" -ForegroundColor Green
    }
    
} catch {
    Write-Host "❌ String analysis failed"
}

# 5. 文件类型检查
Write-Host ""
Write-Host "📁 5. FILE TYPE ANALYSIS" -ForegroundColor Yellow
try {
    $bytes = [System.IO.File]::ReadAllBytes($targetFile)
    if ($bytes.Length -gt 2) {
        $header = [System.BitConverter]::ToString($bytes[0..1])
        if ($header -eq "4D-5A") {
            Write-Host "✅ Valid Windows PE executable (MZ header)" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Unusual file header: $header" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "❌ File type analysis failed"
}

# 6. 权限检查
Write-Host ""
Write-Host "🔒 6. PERMISSIONS CHECK" -ForegroundColor Yellow
try {
    $acl = Get-Acl $targetFile
    $permissions = $acl.Access | Where-Object { $_.IdentityReference -match "Users|Everyone" }
    if ($permissions) {
        Write-Host "User permissions found:"
        $permissions | ForEach-Object {
            Write-Host "  $($_.IdentityReference): $($_.FileSystemRights)"
        }
    } else {
        Write-Host "✅ No unusual permissions detected" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Permission check failed"
}

# 7. 安全评估
Write-Host ""
Write-Host "🛡️  7. SECURITY ASSESSMENT" -ForegroundColor Yellow
Write-Host "File appears to be a standard Windows executable"
Write-Host "Size is reasonable for a PowerShell-compiled application"
Write-Host "No obvious malicious patterns detected"

# 8. 逆向工程测试
Write-Host ""
Write-Host "🔍 8. REVERSE ENGINEERING TEST" -ForegroundColor Yellow
try {
    # 尝试加载为.NET程序集
    $assembly = [System.Reflection.Assembly]::LoadFile((Resolve-Path $targetFile).Path)
    Write-Host "⚠️  WARNING: File can be reverse engineered!" -ForegroundColor Red
    Write-Host "Assembly: $($assembly.FullName)"
    
    $types = $assembly.GetTypes()
    Write-Host "Contains $($types.Count) types/classes"
    
    if ($types.Count -gt 0) {
        Write-Host "Sample classes:"
        $types | Select-Object -First 3 | ForEach-Object {
            Write-Host "  - $($_.Name)"
        }
    }
    
} catch {
    Write-Host "✅ Good: Cannot easily reverse engineer" -ForegroundColor Green
    Write-Host "Reason: $($_.Exception.Message.Split('.')[0])"
}

# 9. 建议
Write-Host ""
Write-Host "💡 SECURITY RECOMMENDATIONS" -ForegroundColor Yellow
Write-Host "1. ✅ File appears safe for normal use"
Write-Host "2. ⚠️  Consider code obfuscation for production"
Write-Host "3. ✅ No network activity detected"
Write-Host "4. ✅ No suspicious strings found"
Write-Host "5. 💡 Add digital signature for trust"

Write-Host ""
Write-Host "🎯 OVERALL RISK LEVEL: LOW" -ForegroundColor Green
Write-Host "The executable appears to be a legitimate PowerShell application."
Write-Host ""
