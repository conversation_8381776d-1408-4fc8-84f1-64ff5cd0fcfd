@echo off
title Augment VIP - Auto Installer
color 0B

echo.
echo ========================================
echo     Augment VIP - Auto Installer
echo ========================================
echo.
echo This program will automatically complete the following operations:
echo 1. Check system environment
echo 2. Create necessary directory structure
echo 3. Download required dependencies
echo 4. Configure runtime environment
echo 5. Provide one-click run options
echo.

set /p confirm=Continue with installation? (Y/N): 
if /i not "%confirm%"=="Y" (
    echo Installation cancelled
    pause
    exit /b 0
)

echo.
echo ========================================
echo         Starting Auto Installation
echo ========================================
echo.

:: Step 1: Check PowerShell
echo [1/5] Checking PowerShell environment...
powershell -Command "Write-Host 'PowerShell Version: ' -NoNewline -ForegroundColor Green; $PSVersionTable.PSVersion"
if errorlevel 1 (
    echo PowerShell not available, please install PowerShell
    pause
    exit /b 1
)

:: Step 2: Create directory structure
echo [2/5] Creating project directory structure...
if not exist "config" mkdir config
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "temp" mkdir temp
if not exist "scripts" mkdir scripts
echo Directory structure created successfully

:: Step 3: Check script files
echo [3/5] Checking script files...
if exist "scripts\clean_code_db.ps1" (
    echo Database cleaning script exists
) else (
    echo Database cleaning script not found
)

if exist "scripts\id_modifier.ps1" (
    echo ID modification script exists
) else (
    echo ID modification script not found
)

if exist "install.ps1" (
    echo Installation script exists
) else (
    echo Installation script not found
)

:: Step 4: Run PowerShell installation script
echo [4/5] Running PowerShell installation script...
if exist "install.ps1" (
    powershell -ExecutionPolicy Bypass -File ".\install.ps1"
    echo PowerShell installation script completed
) else (
    echo PowerShell installation script not found, skipping this step
)

:: Step 5: Create desktop shortcut (optional)
echo [5/5] Creating shortcuts...
set /p shortcut=Create desktop shortcut? (Y/N): 
if /i "%shortcut%"=="Y" (
    set "desktop=%USERPROFILE%\Desktop"
    set "current_dir=%CD%"
    
    :: Create batch shortcut
    echo @echo off > "%desktop%\Augment VIP.bat"
    echo cd /d "%current_dir%" >> "%desktop%\Augment VIP.bat"
    echo call "OneClick-Run.bat" >> "%desktop%\Augment VIP.bat"
    
    echo Desktop shortcut created
)

echo.
echo ========================================
echo         Installation Complete!
echo ========================================
echo.
echo Installation completed successfully! You can now:
echo.
echo 1. Double-click "OneClick-Run.bat" to use the graphical interface
echo 2. Run PowerShell scripts for advanced operations
echo 3. Use desktop shortcut (if created)
echo.
echo Important Notes:
echo - Please close VS Code before use
echo - Restart VS Code after modifications
echo - All operations automatically create backups
echo.

set /p run_now=Run the one-click tool now? (Y/N): 
if /i "%run_now%"=="Y" (
    call "OneClick-Run.bat"
) else (
    echo.
    echo You can double-click "OneClick-Run.bat" anytime to use the tool
    pause
)

exit /b 0
