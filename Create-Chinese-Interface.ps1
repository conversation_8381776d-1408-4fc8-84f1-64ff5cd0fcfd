# 创建中文界面版本
# Create Chinese Interface Version

param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    [string]$OutputFile = "",
    [string]$LicenseKey = "AKDJFDHSKOMGRIOINOFWEOIPEWFIOM3289589894393290543"
)

Write-Host "🇨🇳 正在创建中文界面版本..." -ForegroundColor Cyan

if (-not (Test-Path $InputFile)) {
    Write-Host "❌ 输入文件不存在: $InputFile" -ForegroundColor Red
    exit 1
}

if ($OutputFile -eq "") {
    $OutputFile = $InputFile.Replace(".ps1", "-Chinese.ps1")
}

# 生成许可证哈希
$licenseHash = [System.Security.Cryptography.SHA256]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($LicenseKey))
$licenseHashString = [System.BitConverter]::ToString($licenseHash).Replace("-", "").ToLower()

Write-Host "🔑 许可证哈希: $($licenseHashString.Substring(0,16))..." -ForegroundColor Green

# 创建中文界面的许可证保护代码
$chineseInterfaceCode = @"
# AUGMENT VIP 工具 - 中文企业安全版
# 此版本需要有效的许可证密钥才能运行

# 设置控制台编码为UTF-8以正确显示中文
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
`$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "                AUGMENT VIP 工具 - 企业安全版                  " -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "产品名称: Augment VIP 工具企业安全版" -ForegroundColor White
Write-Host "版本信息: v2.0 中文许可证版" -ForegroundColor White
Write-Host "保护等级: 需要许可证密钥" -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

# 许可证验证函数
function Test-LicenseKey {
    param([string]`$InputKey)
    
    try {
        if (-not `$InputKey -or `$InputKey.Length -eq 0) {
            return `$false
        }
        
        # 计算输入密钥的哈希值
        `$keyBytes = [System.Text.Encoding]::UTF8.GetBytes(`$InputKey.Trim())
        `$sha256 = [System.Security.Cryptography.SHA256]::Create()
        `$hashBytes = `$sha256.ComputeHash(`$keyBytes)
        `$inputHash = [System.BitConverter]::ToString(`$hashBytes).Replace("-", "").ToLower()
        `$sha256.Dispose()
        
        # 预期的哈希值
        `$expectedHash = "$licenseHashString"
        
        # 比较哈希值
        return (`$inputHash -eq `$expectedHash)
    } catch {
        return `$false
    }
}

# 提示用户输入许可证密钥
Write-Host "请输入您的许可证密钥以继续使用:" -ForegroundColor Yellow
Write-Host "（许可证密钥区分大小写，请准确输入）" -ForegroundColor Gray
Write-Host ""

`$userInput = Read-Host "许可证密钥"

# 验证许可证
Write-Host ""
Write-Host "正在验证许可证..." -ForegroundColor Yellow

if (Test-LicenseKey -InputKey `$userInput) {
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Green
    Write-Host "                      许可证验证成功                           " -ForegroundColor Green
    Write-Host "================================================================" -ForegroundColor Green
    Write-Host "状态信息: 许可证密钥有效" -ForegroundColor White
    Write-Host "授权状态: 已获得授权" -ForegroundColor White
    Write-Host "验证时间: `$(Get-Date -Format 'yyyy年MM月dd日 HH:mm:ss')" -ForegroundColor White
    Write-Host "用户信息: `$env:USERNAME@`$env:COMPUTERNAME" -ForegroundColor White
    Write-Host "================================================================" -ForegroundColor Green
    Write-Host ""
    
    # 记录合法访问
    try {
        `$logEntry = "`$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - 授权访问 - 用户: `$env:USERNAME - 计算机: `$env:COMPUTERNAME"
        `$logDir = "logs"
        if (-not (Test-Path `$logDir)) {
            New-Item -ItemType Directory -Path `$logDir -Force | Out-Null
        }
        Add-Content -Path "`$logDir\license-access.log" -Value `$logEntry -Encoding UTF8
    } catch {
        # 忽略日志错误
    }
    
    Write-Host "正在启动应用程序..." -ForegroundColor Green
    Start-Sleep 2
    
    # 执行安全检查
    Write-Host "[安全检查] 正在初始化安全检查..." -ForegroundColor Yellow
    
    # 安全检查函数
    function Invoke-SecurityCheck {
        `$threats = @()
        
        # 检查1: 调试器检测
        if ([System.Diagnostics.Debugger]::IsAttached) {
            `$threats += "检测到调试器附加"
        }
        
        # 检查2: 虚拟机检测
        try {
            `$bios = Get-WmiObject -Class Win32_BIOS -ErrorAction SilentlyContinue
            if (`$bios -and (`$bios.Manufacturer -match "VMware|VirtualBox|Microsoft Corporation|Xen|QEMU")) {
                `$threats += "检测到虚拟机环境"
            }
        } catch { }
        
        # 检查3: 分析工具检测
        try {
            `$suspiciousProcesses = @("ollydbg", "x64dbg", "windbg", "ida", "processhacker", "procmon", "procexp", "wireshark", "fiddler", "burpsuite")
            `$runningProcesses = Get-Process | ForEach-Object { `$_.ProcessName.ToLower() }
            
            foreach (`$tool in `$suspiciousProcesses) {
                if (`$runningProcesses -contains `$tool) {
                    `$threats += "检测到分析工具: `$tool"
                }
            }
        } catch { }
        
        # 检查4: 调试环境变量
        if (`$env:_NT_SYMBOL_PATH -or `$env:_NT_DEBUGGER_EXTENSION_PATH) {
            `$threats += "检测到调试环境"
        }
        
        # 检查5: 沙盒检测
        `$suspiciousUsers = @("sandbox", "malware", "virus", "sample", "test", "analyst")
        `$currentUser = `$env:USERNAME.ToLower()
        if (`$suspiciousUsers -contains `$currentUser) {
            `$threats += "检测到沙盒环境"
        }
        
        # 检查6: 可疑计算机名
        `$suspiciousNames = @("sandbox", "malware", "virus", "sample", "test", "analyst", "cuckoo")
        `$computerName = `$env:COMPUTERNAME.ToLower()
        if (`$suspiciousNames -contains `$computerName) {
            `$threats += "检测到可疑计算机名"
        }
        
        # 如果检测到威胁，终止程序
        if (`$threats.Count -gt 0) {
            Write-Host "[安全警告] 检测到威胁!" -ForegroundColor Red
            foreach (`$threat in `$threats) {
                Write-Host "[安全警告] - `$threat" -ForegroundColor Red
            }
            Write-Host "[安全警告] 因安全原因，应用程序将被终止。" -ForegroundColor Red
            Write-Host "[安全警告] 本软件受到反逆向工程保护。" -ForegroundColor Red
            Start-Sleep 3
            exit 1
        }
        
        return `$true
    }
    
    # 执行初始安全检查
    if (-not (Invoke-SecurityCheck)) {
        exit 1
    }
    
    Write-Host "[安全检查] 安全检查通过。正在启动应用程序..." -ForegroundColor Green
    
    # 启动持续监控
    `$monitorJob = Start-Job -ScriptBlock {
        while (`$true) {
            Start-Sleep 5
            
            # 重新运行安全检查
            if ([System.Diagnostics.Debugger]::IsAttached) {
                Write-Host "[安全警告] 运行时检测到威胁！正在终止..." -ForegroundColor Red
                Stop-Process -Name "powershell" -Force -ErrorAction SilentlyContinue
                break
            }
            
            # 检查新的分析工具
            `$suspiciousProcesses = @("ollydbg", "x64dbg", "windbg", "ida", "processhacker", "procmon", "procexp")
            `$runningProcesses = Get-Process | ForEach-Object { `$_.ProcessName.ToLower() }
            
            foreach (`$tool in `$suspiciousProcesses) {
                if (`$runningProcesses -contains `$tool) {
                    Write-Host "[安全警告] 运行时检测到分析工具: `$tool！正在终止..." -ForegroundColor Red
                    Stop-Process -Name "powershell" -Force -ErrorAction SilentlyContinue
                    break
                }
            }
        }
    }
    
    Write-Host "[安全监控] 持续监控已激活" -ForegroundColor Green
    
    # 加载应用程序（中文界面版本）
    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName System.Drawing
    
    # 创建主窗体
    `$form = New-Object System.Windows.Forms.Form
    `$form.Text = "[安全版] Augment VIP 工具 - 企业安全版"
    `$form.Size = New-Object System.Drawing.Size(800, 600)
    `$form.StartPosition = "CenterScreen"
    `$form.BackColor = [System.Drawing.Color]::FromArgb(45, 45, 48)
    `$form.ForeColor = [System.Drawing.Color]::White
    
    # 安全状态指示器
    `$securityLabel = New-Object System.Windows.Forms.Label
    `$securityLabel.Text = "[已保护] 企业安全防护已激活 - 所有操作均受监控"
    `$securityLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10, [System.Drawing.FontStyle]::Bold)
    `$securityLabel.ForeColor = [System.Drawing.Color]::LimeGreen
    `$securityLabel.Location = New-Object System.Drawing.Point(20, 20)
    `$securityLabel.Size = New-Object System.Drawing.Size(760, 25)
    `$form.Controls.Add(`$securityLabel)
    
    # 警告标签
    `$warningLabel = New-Object System.Windows.Forms.Label
    `$warningLabel.Text = "[警告] 本应用程序受到调试和逆向工程保护"
    `$warningLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
    `$warningLabel.ForeColor = [System.Drawing.Color]::Orange
    `$warningLabel.Location = New-Object System.Drawing.Point(20, 50)
    `$warningLabel.Size = New-Object System.Drawing.Size(760, 20)
    `$form.Controls.Add(`$warningLabel)
    
    # 主内容区域
    `$textBox = New-Object System.Windows.Forms.TextBox
    `$textBox.Multiline = `$true
    `$textBox.ScrollBars = "Vertical"
    `$textBox.Location = New-Object System.Drawing.Point(20, 100)
    `$textBox.Size = New-Object System.Drawing.Size(740, 400)
    `$textBox.BackColor = [System.Drawing.Color]::FromArgb(30, 30, 30)
    `$textBox.ForeColor = [System.Drawing.Color]::White
    `$textBox.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
    `$textBox.ReadOnly = `$true
    `$form.Controls.Add(`$textBox)
    
    # 添加安全状态信息
    `$textBox.Text = @"
[安全检查] Augment VIP 工具 - 企业安全版
========================================================

已激活的安全保护:
✓ 反调试保护
✓ 虚拟机检测
✓ 分析工具检测
✓ 沙盒环境检测
✓ 持续运行时监控
✓ 进程完整性验证

安全状态: 已保护
监控状态: 激活中
威胁等级: 绿色

本应用程序受到以下攻击保护:
- 静态分析攻击
- 动态调试攻击
- 虚拟机分析
- 沙盒分析
- 进程注入攻击
- 内存转储攻击

任何逆向工程此应用程序的尝试都将被检测到，
应用程序将立即终止。

如需正常使用，请关闭此窗口，应用程序将继续
在完全安全保护下运行。

警告: 本软件受知识产权法保护。
未经授权的逆向工程是被禁止的。
"@
    
    # 测试安全保护按钮
    `$testButton = New-Object System.Windows.Forms.Button
    `$testButton.Text = "测试安全保护"
    `$testButton.Size = New-Object System.Drawing.Size(150, 40)
    `$testButton.Location = New-Object System.Drawing.Point(20, 520)
    `$testButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    `$testButton.ForeColor = [System.Drawing.Color]::White
    `$testButton.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
    `$testButton.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
    `$form.Controls.Add(`$testButton)
    
    `$testButton.Add_Click({
        # 执行另一次安全检查
        if (Invoke-SecurityCheck) {
            [System.Windows.Forms.MessageBox]::Show("安全检查通过！应用程序正在安全环境中运行。", "安全测试", "OK", "Information")
        }
    })
    
    # 关闭按钮
    `$closeButton = New-Object System.Windows.Forms.Button
    `$closeButton.Text = "关闭"
    `$closeButton.Size = New-Object System.Drawing.Size(100, 40)
    `$closeButton.Location = New-Object System.Drawing.Point(190, 520)
    `$closeButton.BackColor = [System.Drawing.Color]::FromArgb(120, 120, 120)
    `$closeButton.ForeColor = [System.Drawing.Color]::White
    `$closeButton.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
    `$closeButton.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
    `$form.Controls.Add(`$closeButton)
    
    `$closeButton.Add_Click({
        `$form.Close()
    })
    
    # 窗体关闭事件
    `$form.Add_FormClosing({
        # 停止监控作业
        if (`$monitorJob) {
            Stop-Job `$monitorJob -ErrorAction SilentlyContinue
            Remove-Job `$monitorJob -ErrorAction SilentlyContinue
        }
        Write-Host "[安全检查] 应用程序已安全关闭" -ForegroundColor Green
    })
    
    # 显示窗体
    Write-Host "[安全检查] 正在启动安全应用程序界面..." -ForegroundColor Green
    `$form.ShowDialog() | Out-Null
    
    # 清理
    if (`$monitorJob) {
        Stop-Job `$monitorJob -ErrorAction SilentlyContinue
        Remove-Job `$monitorJob -ErrorAction SilentlyContinue
    }
    
    Write-Host "[安全检查] 程序已安全终止" -ForegroundColor Green
    
} else {
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Red
    Write-Host "                      许可证验证失败                           " -ForegroundColor Red
    Write-Host "================================================================" -ForegroundColor Red
    Write-Host "错误信息: 无效的许可证密钥" -ForegroundColor White
    Write-Host "解决方案: 请联系软件提供商获取有效的许可证" -ForegroundColor White
    Write-Host "技术支持: <EMAIL>" -ForegroundColor White
    Write-Host "警告信息: 未经授权使用本软件是违法行为" -ForegroundColor Yellow
    Write-Host "================================================================" -ForegroundColor Red
    Write-Host ""
    
    # 记录未授权访问尝试
    try {
        `$logEntry = "`$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - 未授权访问尝试 - 用户: `$env:USERNAME - 计算机: `$env:COMPUTERNAME - 输入: `$(`$userInput.Substring(0, [Math]::Min(10, `$userInput.Length)))..."
        `$logDir = "logs"
        if (-not (Test-Path `$logDir)) {
            New-Item -ItemType Directory -Path `$logDir -Force | Out-Null
        }
        Add-Content -Path "`$logDir\license-violations.log" -Value `$logEntry -Encoding UTF8
    } catch {
        # 忽略日志错误
    }
    
    Write-Host "应用程序将在3秒后退出..." -ForegroundColor Red
    Start-Sleep 3
    exit 1
}
"@

# 保存中文界面代码
$chineseInterfaceCode | Out-File $OutputFile -Encoding UTF8

Write-Host ""
Write-Host "✅ 中文界面版本创建成功!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 中文界面特性:" -ForegroundColor Cyan
Write-Host "  ✓ 完全中文化的用户界面" -ForegroundColor Green
Write-Host "  ✓ 中文安全提示和警告信息" -ForegroundColor Green
Write-Host "  ✓ 中文许可证验证界面" -ForegroundColor Green
Write-Host "  ✓ 中文安全状态显示" -ForegroundColor Green
Write-Host "  ✓ 中文日志记录" -ForegroundColor Green
Write-Host "  ✓ 微软雅黑字体支持" -ForegroundColor Green
Write-Host ""
Write-Host "🔑 许可证信息:" -ForegroundColor Yellow
Write-Host "  许可证密钥: $LicenseKey" -ForegroundColor White
Write-Host "  哈希值: $($licenseHashString.Substring(0,32))..." -ForegroundColor Gray
Write-Host ""
Write-Host "📁 输出文件: $OutputFile" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 使用说明:" -ForegroundColor Yellow
Write-Host "  1. 运行程序时会显示中文许可证验证界面" -ForegroundColor White
Write-Host "  2. 输入正确的许可证密钥后程序启动" -ForegroundColor White
Write-Host "  3. 所有界面元素和提示信息均为中文" -ForegroundColor White
Write-Host "  4. 安全检查和警告信息也使用中文显示" -ForegroundColor White
