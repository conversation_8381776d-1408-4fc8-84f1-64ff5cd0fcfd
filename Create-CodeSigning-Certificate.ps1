# 创建代码签名证书和签名工具
# Create Code Signing Certificate and Signing Tools

Write-Host "🔐 AUGMENT VIP TOOL - 代码签名证书创建工具" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# 1. 创建自签名代码签名证书
function Create-SelfSignedCertificate {
    Write-Host "📜 1. 创建自签名代码签名证书..." -ForegroundColor Yellow
    
    try {
        # 检查是否已存在证书
        $existingCert = Get-ChildItem -Path Cert:\CurrentUser\My | Where-Object { $_.Subject -like "*Augment VIP Tool*" }
        
        if ($existingCert) {
            Write-Host "✅ 发现现有证书: $($existingCert.Thumbprint)" -ForegroundColor Green
            return $existingCert
        }
        
        # 创建新的自签名证书
        $cert = New-SelfSignedCertificate -Type CodeSigningCert -Subject "CN=Augment VIP Tool, O=Augment Code, C=CN" -KeyUsage DigitalSignature -FriendlyName "Augment VIP Tool Code Signing" -CertStoreLocation Cert:\CurrentUser\My -KeyLength 2048 -Provider "Microsoft Enhanced RSA and AES Cryptographic Provider" -KeyExportPolicy Exportable -KeySpec Signature -KeyUsageProperty Sign -TextExtension @("*********={text}*******.*******.3", "*********={text}")
        
        Write-Host "✅ 证书创建成功!" -ForegroundColor Green
        Write-Host "   证书指纹: $($cert.Thumbprint)" -ForegroundColor Cyan
        Write-Host "   主题: $($cert.Subject)" -ForegroundColor Cyan
        Write-Host "   有效期: $($cert.NotBefore) 到 $($cert.NotAfter)" -ForegroundColor Cyan
        
        # 将证书添加到受信任的根证书颁发机构（可选）
        $rootStore = New-Object System.Security.Cryptography.X509Certificates.X509Store([System.Security.Cryptography.X509Certificates.StoreName]::Root, [System.Security.Cryptography.X509Certificates.StoreLocation]::CurrentUser)
        $rootStore.Open([System.Security.Cryptography.X509Certificates.OpenFlags]::ReadWrite)
        $rootStore.Add($cert)
        $rootStore.Close()
        
        Write-Host "✅ 证书已添加到受信任的根证书颁发机构" -ForegroundColor Green
        
        return $cert
        
    } catch {
        Write-Host "❌ 证书创建失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 2. 导出证书到文件
function Export-Certificate {
    param($Certificate)
    
    Write-Host "📤 2. 导出证书到文件..." -ForegroundColor Yellow
    
    try {
        # 导出公钥证书 (.cer)
        $certBytes = $Certificate.Export([System.Security.Cryptography.X509Certificates.X509ContentType]::Cert)
        [System.IO.File]::WriteAllBytes("AugmentVIP-CodeSigning.cer", $certBytes)
        Write-Host "✅ 公钥证书已导出: AugmentVIP-CodeSigning.cer" -ForegroundColor Green
        
        # 导出私钥证书 (.pfx) - 需要密码保护
        $password = "AugmentVIP2025!"
        $pfxBytes = $Certificate.Export([System.Security.Cryptography.X509Certificates.X509ContentType]::Pfx, $password)
        [System.IO.File]::WriteAllBytes("AugmentVIP-CodeSigning.pfx", $pfxBytes)
        Write-Host "✅ 私钥证书已导出: AugmentVIP-CodeSigning.pfx" -ForegroundColor Green
        Write-Host "   密码: $password" -ForegroundColor Yellow
        
        return $true
    } catch {
        Write-Host "❌ 证书导出失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 3. 创建签名脚本
function Create-SigningScript {
    Write-Host "📝 3. 创建自动签名脚本..." -ForegroundColor Yellow
    
    $signingScript = @'
# Augment VIP Tool - 自动代码签名脚本
param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath,
    [string]$CertificatePath = "AugmentVIP-CodeSigning.pfx",
    [string]$Password = "AugmentVIP2025!",
    [string]$TimestampServer = "http://timestamp.digicert.com"
)

Write-Host "🔐 开始代码签名..." -ForegroundColor Cyan
Write-Host "文件: $FilePath" -ForegroundColor White

try {
    # 检查文件是否存在
    if (-not (Test-Path $FilePath)) {
        throw "文件不存在: $FilePath"
    }
    
    # 检查证书是否存在
    if (-not (Test-Path $CertificatePath)) {
        throw "证书文件不存在: $CertificatePath"
    }
    
    # 执行签名
    $result = Set-AuthenticodeSignature -FilePath $FilePath -Certificate (Get-PfxCertificate -FilePath $CertificatePath) -TimestampServer $TimestampServer
    
    if ($result.Status -eq "Valid") {
        Write-Host "✅ 签名成功!" -ForegroundColor Green
        Write-Host "   状态: $($result.Status)" -ForegroundColor Green
        Write-Host "   签名者: $($result.SignerCertificate.Subject)" -ForegroundColor Cyan
        Write-Host "   时间戳: $($result.TimeStamperCertificate.Subject)" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ 签名状态: $($result.Status)" -ForegroundColor Yellow
        Write-Host "   状态消息: $($result.StatusMessage)" -ForegroundColor Yellow
    }
    
    # 验证签名
    $verification = Get-AuthenticodeSignature -FilePath $FilePath
    Write-Host "🔍 签名验证结果: $($verification.Status)" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ 签名失败: $($_.Exception.Message)" -ForegroundColor Red
}
'@
    
    $signingScript | Out-File "Sign-AugmentVIP.ps1" -Encoding UTF8
    Write-Host "✅ 签名脚本已创建: Sign-AugmentVIP.ps1" -ForegroundColor Green
}

# 4. 批量签名所有EXE文件
function Sign-AllExecutables {
    param($Certificate)
    
    Write-Host "📦 4. 批量签名所有可执行文件..." -ForegroundColor Yellow
    
    $exeFiles = Get-ChildItem -Path "." -Filter "*.exe" -Recurse | Where-Object { $_.Name -like "*AugmentVIP*" }
    
    if ($exeFiles.Count -eq 0) {
        Write-Host "⚠️ 未找到需要签名的EXE文件" -ForegroundColor Yellow
        return
    }
    
    Write-Host "发现 $($exeFiles.Count) 个文件需要签名:" -ForegroundColor Cyan
    $exeFiles | ForEach-Object { Write-Host "  - $($_.FullName)" -ForegroundColor White }
    
    foreach ($file in $exeFiles) {
        try {
            Write-Host "正在签名: $($file.Name)..." -ForegroundColor Yellow
            
            $result = Set-AuthenticodeSignature -FilePath $file.FullName -Certificate $Certificate -TimestampServer "http://timestamp.digicert.com"
            
            if ($result.Status -eq "Valid") {
                Write-Host "  ✅ $($file.Name) 签名成功" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️ $($file.Name) 签名状态: $($result.Status)" -ForegroundColor Yellow
            }
            
        } catch {
            Write-Host "  ❌ $($file.Name) 签名失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 主执行流程
Write-Host "开始创建代码签名证书和工具..." -ForegroundColor Green
Write-Host ""

# 创建证书
$certificate = Create-SelfSignedCertificate
if ($certificate) {
    # 导出证书
    Export-Certificate -Certificate $certificate
    
    # 创建签名脚本
    Create-SigningScript
    
    # 批量签名
    Sign-AllExecutables -Certificate $certificate
    
    Write-Host ""
    Write-Host "🎉 代码签名设置完成!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 生成的文件:" -ForegroundColor Cyan
    Write-Host "  - AugmentVIP-CodeSigning.cer (公钥证书)" -ForegroundColor White
    Write-Host "  - AugmentVIP-CodeSigning.pfx (私钥证书)" -ForegroundColor White
    Write-Host "  - Sign-AugmentVIP.ps1 (自动签名脚本)" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 使用方法:" -ForegroundColor Cyan
    Write-Host "  .\Sign-AugmentVIP.ps1 -FilePath 'your-file.exe'" -ForegroundColor White
    
} else {
    Write-Host "❌ 代码签名设置失败" -ForegroundColor Red
}
