# PowerShell代码混淆工具
# PowerShell Code Obfuscation Tool

param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    [string]$OutputFile = "",
    [switch]$Advanced
)

Write-Host "🔒 AUGMENT VIP TOOL - PowerShell代码混淆工具" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

if (-not (Test-Path $InputFile)) {
    Write-Host "❌ 输入文件不存在: $InputFile" -ForegroundColor Red
    exit 1
}

if ($OutputFile -eq "") {
    $OutputFile = $InputFile.Replace(".ps1", "-Obfuscated.ps1")
}

Write-Host "📁 输入文件: $InputFile" -ForegroundColor Green
Write-Host "📁 输出文件: $OutputFile" -ForegroundColor Green
Write-Host ""

# 读取原始代码
$originalCode = Get-Content $InputFile -Raw

# 1. 变量名混淆
function Obfuscate-Variables {
    param([string]$Code)
    
    Write-Host "🔤 1. 混淆变量名..." -ForegroundColor Yellow
    
    # 生成随机变量名映射
    $variableMap = @{}
    $variablePattern = '\$([a-zA-Z_][a-zA-Z0-9_]*)'
    $matches = [regex]::Matches($Code, $variablePattern)
    
    $usedNames = @()
    foreach ($match in $matches) {
        $varName = $match.Groups[1].Value
        if (-not $variableMap.ContainsKey($varName) -and $varName -notmatch '^(true|false|null|_|args|input|output|error|host|home|profile|pshome|pwd)$') {
            do {
                $newName = -join ((1..8) | ForEach-Object { [char]((65..90) + (97..122) | Get-Random) })
            } while ($usedNames -contains $newName)
            
            $variableMap[$varName] = $newName
            $usedNames += $newName
        }
    }
    
    # 替换变量名
    foreach ($oldVar in $variableMap.Keys) {
        $newVar = $variableMap[$oldVar]
        $Code = $Code -replace "\`$$oldVar\b", "`$$newVar"
    }
    
    Write-Host "   混淆了 $($variableMap.Count) 个变量" -ForegroundColor Green
    return $Code
}

# 2. 函数名混淆
function Obfuscate-Functions {
    param([string]$Code)
    
    Write-Host "🔧 2. 混淆函数名..." -ForegroundColor Yellow
    
    $functionMap = @{}
    $functionPattern = 'function\s+([a-zA-Z_][a-zA-Z0-9_-]*)'
    $matches = [regex]::Matches($Code, $functionPattern)
    
    foreach ($match in $matches) {
        $funcName = $match.Groups[1].Value
        if (-not $functionMap.ContainsKey($funcName)) {
            $newName = "Func" + (-join ((1..6) | ForEach-Object { [char]((65..90) | Get-Random) }))
            $functionMap[$funcName] = $newName
        }
    }
    
    # 替换函数定义和调用
    foreach ($oldFunc in $functionMap.Keys) {
        $newFunc = $functionMap[$oldFunc]
        $Code = $Code -replace "\bfunction\s+$oldFunc\b", "function $newFunc"
        $Code = $Code -replace "\b$oldFunc\b", $newFunc
    }
    
    Write-Host "   混淆了 $($functionMap.Count) 个函数" -ForegroundColor Green
    return $Code
}

# 3. 字符串混淆
function Obfuscate-Strings {
    param([string]$Code)
    
    Write-Host "📝 3. 混淆字符串..." -ForegroundColor Yellow
    
    # 查找字符串并进行Base64编码
    $stringPattern = '"([^"\\]*(\\.[^"\\]*)*)"'
    $Code = [regex]::Replace($Code, $stringPattern, {
        param($match)
        $originalString = $match.Groups[1].Value
        
        # 跳过太短的字符串和特殊字符串
        if ($originalString.Length -lt 5 -or $originalString -match '^[0-9\s\-\.\:]+$') {
            return $match.Value
        }
        
        try {
            $bytes = [System.Text.Encoding]::UTF8.GetBytes($originalString)
            $base64 = [System.Convert]::ToBase64String($bytes)
            return "[System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String('$base64'))"
        } catch {
            return $match.Value
        }
    })
    
    Write-Host "   字符串已编码" -ForegroundColor Green
    return $Code
}

# 4. 添加垃圾代码
function Add-JunkCode {
    param([string]$Code)
    
    Write-Host "🗑️ 4. 添加垃圾代码..." -ForegroundColor Yellow
    
    $junkCode = @"
# Obfuscated code - Do not modify
`$null = @(
    [System.GC]::Collect()
    [System.Threading.Thread]::Sleep(1)
    Get-Date | Out-Null
    `$env:COMPUTERNAME | Out-Null
)

# Random variables for obfuscation
`$ObfVar1 = Get-Random
`$ObfVar2 = [System.Environment]::TickCount
`$ObfVar3 = [System.Guid]::NewGuid().ToString()

"@
    
    $Code = $junkCode + "`n" + $Code
    
    Write-Host "   已添加垃圾代码" -ForegroundColor Green
    return $Code
}

# 5. 控制流混淆
function Obfuscate-ControlFlow {
    param([string]$Code)
    
    Write-Host "🔀 5. 混淆控制流..." -ForegroundColor Yellow
    
    # 添加无用的条件判断
    $obfuscatedCode = @"
# Control flow obfuscation
if (`$true -eq `$true) {
    if ((Get-Date).Year -gt 2020) {
        # Original code starts here
$Code
        # Original code ends here
    }
}
"@
    
    Write-Host "   控制流已混淆" -ForegroundColor Green
    return $obfuscatedCode
}

# 6. 高级混淆（可选）
function Advanced-Obfuscation {
    param([string]$Code)
    
    Write-Host "⚡ 6. 高级混淆..." -ForegroundColor Yellow
    
    # 将整个代码转换为Base64并动态执行
    $bytes = [System.Text.Encoding]::UTF8.GetBytes($Code)
    $base64Code = [System.Convert]::ToBase64String($bytes)
    
    $advancedCode = @"
# Advanced obfuscated PowerShell code
# Generated by Augment VIP Tool Obfuscator

`$EncodedCode = '$base64Code'
`$DecodedBytes = [System.Convert]::FromBase64String(`$EncodedCode)
`$DecodedCode = [System.Text.Encoding]::UTF8.GetString(`$DecodedBytes)

# Anti-debugging check
if ([System.Diagnostics.Debugger]::IsAttached) {
    Write-Host "Debugging detected. Exiting..." -ForegroundColor Red
    exit
}

# Execute decoded code
Invoke-Expression `$DecodedCode
"@
    
    Write-Host "   高级混淆完成" -ForegroundColor Green
    return $advancedCode
}

# 执行混淆
Write-Host "开始混淆代码..." -ForegroundColor Green
Write-Host ""

$obfuscatedCode = $originalCode

# 基本混淆
$obfuscatedCode = Obfuscate-Variables -Code $obfuscatedCode
$obfuscatedCode = Obfuscate-Functions -Code $obfuscatedCode
$obfuscatedCode = Obfuscate-Strings -Code $obfuscatedCode
$obfuscatedCode = Add-JunkCode -Code $obfuscatedCode
$obfuscatedCode = Obfuscate-ControlFlow -Code $obfuscatedCode

# 高级混淆（如果启用）
if ($Advanced) {
    $obfuscatedCode = Advanced-Obfuscation -Code $obfuscatedCode
}

# 保存混淆后的代码
$obfuscatedCode | Out-File $OutputFile -Encoding UTF8

Write-Host ""
Write-Host "🎉 代码混淆完成!" -ForegroundColor Green
Write-Host "📊 统计信息:" -ForegroundColor Cyan
Write-Host "   原始大小: $($originalCode.Length) 字符" -ForegroundColor White
Write-Host "   混淆大小: $($obfuscatedCode.Length) 字符" -ForegroundColor White
Write-Host "   大小变化: +$($obfuscatedCode.Length - $originalCode.Length) 字符" -ForegroundColor White
Write-Host ""
Write-Host "💡 使用方法:" -ForegroundColor Cyan
Write-Host "   基本混淆: .\Obfuscate-PowerShell.ps1 -InputFile 'script.ps1'" -ForegroundColor White
Write-Host "   高级混淆: .\Obfuscate-PowerShell.ps1 -InputFile 'script.ps1' -Advanced" -ForegroundColor White
