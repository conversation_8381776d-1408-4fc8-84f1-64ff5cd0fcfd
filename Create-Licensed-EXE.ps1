# Create Licensed EXE Version
Write-Host "Creating licensed EXE version..." -ForegroundColor Cyan

# Create C# wrapper with license protection
$csharpCode = @'
using System;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;

namespace AugmentVIPLicensed
{
    class Program
    {
        private static string expectedHash = "db42154cfde3cabadf3521ef55f88a74e8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8";
        
        static void Main(string[] args)
        {
            try
            {
                // Display license information
                Console.WriteLine();
                Console.WriteLine("================================================================");
                Console.WriteLine("           AUGMENT VIP TOOL - ENTERPRISE EDITION               ");
                Console.WriteLine("================================================================");
                Console.WriteLine("Product: Augment VIP Tool Enterprise Security Edition");
                Console.WriteLine("Version: v2.0 Licensed EXE");
                Console.WriteLine("Protection: License Key Required");
                Console.WriteLine("================================================================");
                Console.WriteLine();
                
                // Prompt for license key
                Console.WriteLine("Please enter your license key to continue:");
                Console.WriteLine("(License key is case-sensitive)");
                Console.WriteLine();
                Console.Write("License Key: ");
                
                string userInput = Console.ReadLine();
                
                Console.WriteLine();
                Console.WriteLine("Validating license...");
                
                if (ValidateLicense(userInput))
                {
                    Console.WriteLine();
                    Console.WriteLine("================================================================");
                    Console.WriteLine("                    LICENSE VALIDATION SUCCESS                 ");
                    Console.WriteLine("================================================================");
                    Console.WriteLine("Status: License key is valid");
                    Console.WriteLine("Authorization: Granted");
                    Console.WriteLine($"Validation Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    Console.WriteLine($"User: {Environment.UserName}@{Environment.MachineName}");
                    Console.WriteLine("================================================================");
                    Console.WriteLine();
                    
                    Console.WriteLine("Starting application...");
                    System.Threading.Thread.Sleep(2000);
                    
                    // Launch PowerShell script
                    LaunchPowerShellScript();
                }
                else
                {
                    Console.WriteLine();
                    Console.WriteLine("================================================================");
                    Console.WriteLine("                    LICENSE VALIDATION FAILED                  ");
                    Console.WriteLine("================================================================");
                    Console.WriteLine("Error: Invalid license key");
                    Console.WriteLine("Solution: Please contact software provider for valid license");
                    Console.WriteLine("Support: <EMAIL>");
                    Console.WriteLine("Warning: Unauthorized use of this software is illegal");
                    Console.WriteLine("================================================================");
                    Console.WriteLine();
                    
                    Console.WriteLine("Application will exit in 3 seconds...");
                    System.Threading.Thread.Sleep(3000);
                    Environment.Exit(1);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Environment.Exit(1);
            }
        }
        
        static bool ValidateLicense(string inputKey)
        {
            try
            {
                if (string.IsNullOrEmpty(inputKey))
                    return false;
                
                // Calculate SHA256 hash of input
                using (SHA256 sha256 = SHA256.Create())
                {
                    byte[] keyBytes = Encoding.UTF8.GetBytes(inputKey.Trim());
                    byte[] hashBytes = sha256.ComputeHash(keyBytes);
                    string inputHash = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
                    
                    // Compare with expected hash
                    return inputHash.Equals(expectedHash, StringComparison.OrdinalIgnoreCase);
                }
            }
            catch
            {
                return false;
            }
        }
        
        static void LaunchPowerShellScript()
        {
            try
            {
                ProcessStartInfo psi = new ProcessStartInfo();
                psi.FileName = "powershell.exe";
                psi.Arguments = "-ExecutionPolicy Bypass -File \"AugmentVIP-FINAL-SECURE.ps1\"";
                psi.UseShellExecute = false;
                psi.CreateNoWindow = false;
                
                Process process = Process.Start(psi);
                process.WaitForExit();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to launch application: {ex.Message}");
            }
        }
    }
}
'@

# Save C# source
$csharpCode | Out-File "LicensedWrapper.cs" -Encoding UTF8

# Compile to EXE
$compiler = "$env:WINDIR\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
if (-not (Test-Path $compiler)) {
    $compiler = "$env:WINDIR\Microsoft.NET\Framework\v4.0.30319\csc.exe"
}

if (Test-Path $compiler) {
    $outputFile = "Augment-VIP-ENTERPRISE-SECURITY\AugmentVIP-LICENSED-SECURE.exe"
    $args = @("/out:$outputFile", "/target:exe", "/optimize+", "LicensedWrapper.cs")
    
    Write-Host "Compiling licensed EXE..." -ForegroundColor Yellow
    $process = Start-Process -FilePath $compiler -ArgumentList $args -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "SUCCESS: Licensed EXE created!" -ForegroundColor Green
        
        # Sign the EXE if certificate exists
        if (Test-Path "AugmentVIP-CodeSigning.pfx") {
            try {
                Write-Host "Signing licensed EXE..." -ForegroundColor Yellow
                $cert = Get-PfxCertificate -FilePath "AugmentVIP-CodeSigning.pfx"
                $result = Set-AuthenticodeSignature -FilePath $outputFile -Certificate $cert -TimestampServer "http://timestamp.digicert.com"
                Write-Host "Signing status: $($result.Status)" -ForegroundColor Cyan
            } catch {
                Write-Host "Signing failed: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
        
        # Display file info
        $fileInfo = Get-Item $outputFile
        Write-Host ""
        Write-Host "LICENSED EXE CREATED:" -ForegroundColor Green
        Write-Host "File: $($fileInfo.Name)" -ForegroundColor White
        Write-Host "Size: $($fileInfo.Length) bytes" -ForegroundColor White
        Write-Host "Created: $($fileInfo.CreationTime)" -ForegroundColor White
        
    } else {
        Write-Host "ERROR: Compilation failed" -ForegroundColor Red
    }
} else {
    Write-Host "ERROR: .NET compiler not found" -ForegroundColor Red
}

# Clean up
Remove-Item "LicensedWrapper.cs" -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "LICENSE PROTECTION SUMMARY:" -ForegroundColor Cyan
Write-Host "- PowerShell version: AugmentVIP-LICENSED-SECURE.ps1" -ForegroundColor White
Write-Host "- EXE version: AugmentVIP-LICENSED-SECURE.exe" -ForegroundColor White
Write-Host "- License key: AKDJFDHSKOMGRIOINOFWEOIPEWFIOM3289589894393290543" -ForegroundColor Yellow
Write-Host "- Protection: SHA256 hash validation" -ForegroundColor Green
Write-Host "- Logging: All access attempts logged" -ForegroundColor Green
