# Create Icon for Augment VIP Tool
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

function Create-AugmentVIPIcon {
    param([string]$OutputPath)
    
    try {
        # Create multiple sizes for the icon
        $sizes = @(16, 32, 48, 64, 128, 256)
        $iconImages = @()
        
        foreach ($size in $sizes) {
            # Create bitmap
            $bitmap = New-Object System.Drawing.Bitmap($size, $size)
            $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
            
            # Set high quality rendering
            $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
            $graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias
            
            # Calculate proportional sizes
            $margin = [Math]::Max(2, $size / 16)
            $circleSize = $size - ($margin * 2)
            
            # Draw gradient background circle
            $rect = New-Object System.Drawing.Rectangle($margin, $margin, $circleSize, $circleSize)
            $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
                $rect, 
                [System.Drawing.Color]::FromArgb(0, 120, 215), 
                [System.Drawing.Color]::FromArgb(0, 80, 180), 
                [System.Drawing.Drawing2D.LinearGradientMode]::Vertical
            )
            $graphics.FillEllipse($brush, $rect)
            
            # Draw border
            $borderWidth = [Math]::Max(1, $size / 32)
            $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(0, 60, 120), $borderWidth)
            $graphics.DrawEllipse($pen, $rect)
            
            # Draw text
            $fontSize = [Math]::Max(8, $size / 4)
            $font = New-Object System.Drawing.Font("Arial", $fontSize, [System.Drawing.FontStyle]::Bold)
            $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
            
            # Center the text
            $text = if ($size -ge 32) { "AV" } else { "A" }
            $textSize = $graphics.MeasureString($text, $font)
            $textX = ($size - $textSize.Width) / 2
            $textY = ($size - $textSize.Height) / 2
            
            $graphics.DrawString($text, $font, $textBrush, $textX, $textY)
            
            # Add to collection
            $iconImages += $bitmap
            
            # Cleanup
            $graphics.Dispose()
            $brush.Dispose()
            $pen.Dispose()
            $textBrush.Dispose()
            $font.Dispose()
        }
        
        # Save as ICO file (simplified approach)
        # For a proper multi-size ICO, we'll use the largest bitmap
        $iconImages[5].Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        Write-Host "Icon created: $OutputPath"
        return $true
    }
    catch {
        Write-Host "Error creating icon: $($_.Exception.Message)"
        return $false
    }
}

# Create the icon
$iconPath = Join-Path $PSScriptRoot "augment-vip-icon.png"
Create-AugmentVIPIcon -OutputPath $iconPath
