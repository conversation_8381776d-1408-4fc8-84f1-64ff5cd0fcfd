# 完整性保护模块
# Integrity Protection Module

# 完整性保护类
class IntegrityProtection {
    static [string] $ExpectedHash = ""
    static [string] $SecretKey = "AugmentVIP-Integrity-2025"
    static [bool] $IsVerified = $false
    
    # 计算文件哈希
    static [string] CalculateFileHash([string]$FilePath) {
        try {
            if (-not (Test-Path $FilePath)) {
                throw "File not found: $FilePath"
            }
            
            $fileBytes = [System.IO.File]::ReadAllBytes($FilePath)
            $sha256 = [System.Security.Cryptography.SHA256]::Create()
            $hashBytes = $sha256.ComputeHash($fileBytes)
            $sha256.Dispose()
            
            return [System.BitConverter]::ToString($hashBytes).Replace("-", "").ToLower()
        } catch {
            Write-Host "Error calculating hash: $($_.Exception.Message)" -ForegroundColor Red
            return ""
        }
    }
    
    # 生成HMAC签名
    static [string] GenerateHMAC([string]$Data, [string]$Key) {
        try {
            $keyBytes = [System.Text.Encoding]::UTF8.GetBytes($Key)
            $dataBytes = [System.Text.Encoding]::UTF8.GetBytes($Data)
            
            $hmac = New-Object System.Security.Cryptography.HMACSHA256
            $hmac.Key = $keyBytes
            $hashBytes = $hmac.ComputeHash($dataBytes)
            $hmac.Dispose()
            
            return [System.Convert]::ToBase64String($hashBytes)
        } catch {
            Write-Host "Error generating HMAC: $($_.Exception.Message)" -ForegroundColor Red
            return ""
        }
    }
    
    # 验证完整性
    static [bool] VerifyIntegrity([string]$FilePath) {
        try {
            Write-Host "[INTEGRITY] Verifying file integrity..." -ForegroundColor Yellow
            
            # 计算当前文件哈希
            $currentHash = [IntegrityProtection]::CalculateFileHash($FilePath)
            if ($currentHash -eq "") {
                return $false
            }
            
            # 如果没有预期哈希，生成并保存
            if ([IntegrityProtection]::ExpectedHash -eq "") {
                [IntegrityProtection]::ExpectedHash = $currentHash
                Write-Host "[INTEGRITY] Initial hash recorded: $($currentHash.Substring(0,16))..." -ForegroundColor Green
                [IntegrityProtection]::IsVerified = $true
                return $true
            }
            
            # 比较哈希值
            if ($currentHash -eq [IntegrityProtection]::ExpectedHash) {
                Write-Host "[INTEGRITY] File integrity verified successfully" -ForegroundColor Green
                [IntegrityProtection]::IsVerified = $true
                return $true
            } else {
                Write-Host "[INTEGRITY] WARNING: File integrity check FAILED!" -ForegroundColor Red
                Write-Host "[INTEGRITY] Expected: $([IntegrityProtection]::ExpectedHash.Substring(0,16))..." -ForegroundColor Red
                Write-Host "[INTEGRITY] Current:  $($currentHash.Substring(0,16))..." -ForegroundColor Red
                [IntegrityProtection]::IsVerified = $false
                return $false
            }
            
        } catch {
            Write-Host "[INTEGRITY] Error during verification: $($_.Exception.Message)" -ForegroundColor Red
            [IntegrityProtection]::IsVerified = $false
            return $false
        }
    }
    
    # 运行时完整性检查
    static [void] RuntimeIntegrityCheck() {
        try {
            # 获取当前脚本路径
            $scriptPath = $MyInvocation.ScriptName
            if (-not $scriptPath) {
                $scriptPath = $PSCommandPath
            }
            
            if ($scriptPath) {
                $isValid = [IntegrityProtection]::VerifyIntegrity($scriptPath)
                if (-not $isValid) {
                    Write-Host "[SECURITY] CRITICAL: File tampering detected!" -ForegroundColor Red
                    Write-Host "[SECURITY] The application may have been modified by unauthorized parties." -ForegroundColor Red
                    Write-Host "[SECURITY] For security reasons, execution will be terminated." -ForegroundColor Red
                    
                    # 记录安全事件
                    [IntegrityProtection]::LogSecurityEvent("File tampering detected", $scriptPath)
                    
                    # 安全退出
                    Start-Sleep 3
                    exit 1
                }
            }
        } catch {
            Write-Host "[INTEGRITY] Runtime check error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # 记录安全事件
    static [void] LogSecurityEvent([string]$Event, [string]$Details) {
        try {
            $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            $logEntry = "[$timestamp] SECURITY EVENT: $Event - Details: $Details"
            
            # 创建日志目录
            $logDir = "logs"
            if (-not (Test-Path $logDir)) {
                New-Item -ItemType Directory -Path $logDir -Force | Out-Null
            }
            
            # 写入安全日志
            $logFile = Join-Path $logDir "security-events.log"
            Add-Content -Path $logFile -Value $logEntry -Encoding UTF8
            
            # 也写入Windows事件日志（如果有权限）
            try {
                Write-EventLog -LogName Application -Source "Augment VIP Tool" -EventId 1001 -EntryType Warning -Message $logEntry
            } catch {
                # 忽略事件日志写入失败
            }
            
        } catch {
            Write-Host "[INTEGRITY] Failed to log security event: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # 生成完整性保护的代码
    static [string] GenerateProtectedCode([string]$OriginalCode) {
        try {
            # 计算原始代码的哈希
            $codeBytes = [System.Text.Encoding]::UTF8.GetBytes($OriginalCode)
            $sha256 = [System.Security.Cryptography.SHA256]::Create()
            $hashBytes = $sha256.ComputeHash($codeBytes)
            $codeHash = [System.BitConverter]::ToString($hashBytes).Replace("-", "").ToLower()
            $sha256.Dispose()
            
            # 生成HMAC签名
            $signature = [IntegrityProtection]::GenerateHMAC($codeHash, [IntegrityProtection]::SecretKey)
            
            # 创建受保护的代码
            $protectedCode = @"
# Augment VIP Tool - Integrity Protected Version
# DO NOT MODIFY - This file is protected against tampering

# Integrity Protection Module
class IntegrityProtection {
    static [string] `$ExpectedHash = "$codeHash"
    static [string] `$ExpectedSignature = "$signature"
    static [string] `$SecretKey = "$([IntegrityProtection]::SecretKey)"
    
    static [bool] VerifyCodeIntegrity([string]`$Code) {
        try {
            # Calculate current code hash
            `$codeBytes = [System.Text.Encoding]::UTF8.GetBytes(`$Code)
            `$sha256 = [System.Security.Cryptography.SHA256]::Create()
            `$hashBytes = `$sha256.ComputeHash(`$codeBytes)
            `$currentHash = [System.BitConverter]::ToString(`$hashBytes).Replace("-", "").ToLower()
            `$sha256.Dispose()
            
            # Verify hash
            if (`$currentHash -ne [IntegrityProtection]::ExpectedHash) {
                return `$false
            }
            
            # Verify HMAC signature
            `$keyBytes = [System.Text.Encoding]::UTF8.GetBytes([IntegrityProtection]::SecretKey)
            `$dataBytes = [System.Text.Encoding]::UTF8.GetBytes(`$currentHash)
            `$hmac = New-Object System.Security.Cryptography.HMACSHA256
            `$hmac.Key = `$keyBytes
            `$signatureBytes = `$hmac.ComputeHash(`$dataBytes)
            `$currentSignature = [System.Convert]::ToBase64String(`$signatureBytes)
            `$hmac.Dispose()
            
            return (`$currentSignature -eq [IntegrityProtection]::ExpectedSignature)
        } catch {
            return `$false
        }
    }
}

# Integrity check before execution
`$originalCode = @'
$OriginalCode
'@

if (-not [IntegrityProtection]::VerifyCodeIntegrity(`$originalCode)) {
    Write-Host "[SECURITY] CRITICAL: Code integrity verification FAILED!" -ForegroundColor Red
    Write-Host "[SECURITY] The application code has been tampered with." -ForegroundColor Red
    Write-Host "[SECURITY] Execution terminated for security reasons." -ForegroundColor Red
    exit 1
}

Write-Host "[SECURITY] Code integrity verified successfully" -ForegroundColor Green

# Execute original code
Invoke-Expression `$originalCode
"@
            
            return $protectedCode
            
        } catch {
            Write-Host "Error generating protected code: $($_.Exception.Message)" -ForegroundColor Red
            return $OriginalCode
        }
    }
}

# 导出函数供外部使用
function Add-IntegrityProtection {
    param(
        [Parameter(Mandatory=$true)]
        [string]$InputFile,
        [string]$OutputFile = ""
    )
    
    Write-Host "🔐 添加完整性保护..." -ForegroundColor Cyan
    
    if (-not (Test-Path $InputFile)) {
        Write-Host "❌ 输入文件不存在: $InputFile" -ForegroundColor Red
        return $false
    }
    
    if ($OutputFile -eq "") {
        $OutputFile = $InputFile.Replace(".ps1", "-Protected.ps1")
    }
    
    try {
        # 读取原始代码
        $originalCode = Get-Content $InputFile -Raw
        
        # 生成受保护的代码
        $protectedCode = [IntegrityProtection]::GenerateProtectedCode($originalCode)
        
        # 保存受保护的代码
        $protectedCode | Out-File $OutputFile -Encoding UTF8
        
        Write-Host "✅ 完整性保护已添加" -ForegroundColor Green
        Write-Host "   输入文件: $InputFile" -ForegroundColor White
        Write-Host "   输出文件: $OutputFile" -ForegroundColor White
        
        return $true
        
    } catch {
        Write-Host "❌ 添加完整性保护失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 使用示例
if ($MyInvocation.InvocationName -eq $MyInvocation.MyCommand.Name) {
    Write-Host "🛡️ AUGMENT VIP TOOL - 完整性保护模块" -ForegroundColor Cyan
    Write-Host "====================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "💡 使用方法:" -ForegroundColor Yellow
    Write-Host "   Add-IntegrityProtection -InputFile 'script.ps1'" -ForegroundColor White
    Write-Host "   Add-IntegrityProtection -InputFile 'script.ps1' -OutputFile 'protected.ps1'" -ForegroundColor White
}
