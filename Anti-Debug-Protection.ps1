# 反调试保护模块
# Anti-Debug Protection Module

# 反调试保护类
class AntiDebugProtection {
    static [bool] $DebugDetected = $false
    static [int] $CheckCount = 0
    static [datetime] $StartTime = (Get-Date)
    
    # 检测调试器是否附加
    static [bool] IsDebuggerAttached() {
        try {
            return [System.Diagnostics.Debugger]::IsAttached
        } catch {
            return $false
        }
    }
    
    # 检测是否在虚拟机中运行
    static [bool] IsRunningInVM() {
        try {
            # 检查常见的虚拟机指标
            $vmIndicators = @(
                # BIOS信息
                (Get-WmiObject -Class Win32_BIOS | Where-Object { $_.Manufacturer -match "VMware|VirtualBox|Microsoft Corporation|Xen|QEMU" }),
                
                # 计算机系统信息
                (Get-WmiObject -Class Win32_ComputerSystem | Where-Object { $_.Manufacturer -match "VMware|VirtualBox|Microsoft Corporation|Xen|QEMU" }),
                
                # 检查虚拟机相关服务
                (Get-Service | Where-Object { $_.Name -match "VMware|VBox|Hyper-V" }),
                
                # 检查虚拟机相关进程
                (Get-Process | Where-Object { $_.ProcessName -match "VMware|VBox|Hyper-V" })
            )
            
            return ($vmIndicators | Where-Object { $_ -ne $null }).Count -gt 0
            
        } catch {
            return $false
        }
    }
    
    # 检测沙盒环境
    static [bool] IsRunningSandbox() {
        try {
            $sandboxIndicators = @()
            
            # 检查用户名
            $suspiciousUsers = @("sandbox", "malware", "virus", "sample", "test", "analyst")
            $currentUser = $env:USERNAME.ToLower()
            if ($suspiciousUsers | Where-Object { $currentUser -contains $_ }) {
                $sandboxIndicators += "Suspicious username"
            }
            
            # 检查计算机名
            $suspiciousNames = @("sandbox", "malware", "virus", "sample", "test", "analyst", "cuckoo")
            $computerName = $env:COMPUTERNAME.ToLower()
            if ($suspiciousNames | Where-Object { $computerName -contains $_ }) {
                $sandboxIndicators += "Suspicious computer name"
            }
            
            # 检查系统运行时间（沙盒通常运行时间很短）
            $uptime = (Get-Date) - (Get-CimInstance -ClassName Win32_OperatingSystem).LastBootUpTime
            if ($uptime.TotalMinutes -lt 10) {
                $sandboxIndicators += "Short system uptime"
            }
            
            # 检查内存大小（沙盒通常内存较小）
            $totalMemory = (Get-CimInstance -ClassName Win32_ComputerSystem).TotalPhysicalMemory / 1GB
            if ($totalMemory -lt 2) {
                $sandboxIndicators += "Low memory"
            }
            
            return $sandboxIndicators.Count -gt 1
            
        } catch {
            return $false
        }
    }
    
    # 时间检查（检测调试时的时间异常）
    static [bool] TimeCheck() {
        try {
            $startTime = Get-Date
            Start-Sleep -Milliseconds 100
            $endTime = Get-Date
            $elapsed = ($endTime - $startTime).TotalMilliseconds
            
            # 如果时间差异过大，可能在调试中
            return $elapsed -gt 500
            
        } catch {
            return $false
        }
    }
    
    # 检测分析工具
    static [bool] DetectAnalysisTools() {
        try {
            $analysisTools = @(
                "ollydbg", "x64dbg", "windbg", "ida", "ghidra", "radare2",
                "processhacker", "procmon", "procexp", "wireshark",
                "fiddler", "burpsuite", "cheatengine", "artmoney"
            )
            
            $runningProcesses = Get-Process | ForEach-Object { $_.ProcessName.ToLower() }
            
            foreach ($tool in $analysisTools) {
                if ($runningProcesses -contains $tool) {
                    return $true
                }
            }
            
            return $false
            
        } catch {
            return $false
        }
    }
    
    # 综合反调试检查
    static [bool] PerformAntiDebugCheck() {
        [AntiDebugProtection]::CheckCount++
        
        try {
            Write-Host "[ANTI-DEBUG] Performing security check #$([AntiDebugProtection]::CheckCount)..." -ForegroundColor Yellow
            
            $threats = @()
            
            # 检查调试器
            if ([AntiDebugProtection]::IsDebuggerAttached()) {
                $threats += "Debugger attached"
            }
            
            # 检查虚拟机
            if ([AntiDebugProtection]::IsRunningInVM()) {
                $threats += "Virtual machine detected"
            }
            
            # 检查沙盒
            if ([AntiDebugProtection]::IsRunningSandbox()) {
                $threats += "Sandbox environment detected"
            }
            
            # 时间检查
            if ([AntiDebugProtection]::TimeCheck()) {
                $threats += "Time anomaly detected"
            }
            
            # 检查分析工具
            if ([AntiDebugProtection]::DetectAnalysisTools()) {
                $threats += "Analysis tools detected"
            }
            
            if ($threats.Count -gt 0) {
                [AntiDebugProtection]::DebugDetected = $true
                Write-Host "[ANTI-DEBUG] THREAT DETECTED!" -ForegroundColor Red
                foreach ($threat in $threats) {
                    Write-Host "[ANTI-DEBUG] - $threat" -ForegroundColor Red
                }
                return $true
            } else {
                Write-Host "[ANTI-DEBUG] Environment check passed" -ForegroundColor Green
                return $false
            }
            
        } catch {
            Write-Host "[ANTI-DEBUG] Check failed: $($_.Exception.Message)" -ForegroundColor Yellow
            return $false
        }
    }
    
    # 反调试响应
    static [void] AntiDebugResponse() {
        Write-Host "[SECURITY] CRITICAL: Debugging/Analysis attempt detected!" -ForegroundColor Red
        Write-Host "[SECURITY] This application is protected against reverse engineering." -ForegroundColor Red
        Write-Host "[SECURITY] Execution will be terminated for security reasons." -ForegroundColor Red
        
        # 记录安全事件
        try {
            $logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Anti-debug triggered - Check count: $([AntiDebugProtection]::CheckCount)"
            $logDir = "logs"
            if (-not (Test-Path $logDir)) {
                New-Item -ItemType Directory -Path $logDir -Force | Out-Null
            }
            Add-Content -Path "$logDir\anti-debug.log" -Value $logEntry -Encoding UTF8
        } catch {
            # 忽略日志错误
        }
        
        # 混淆退出过程
        $random = Get-Random -Minimum 1 -Maximum 5
        Start-Sleep $random
        
        # 多种退出方式
        switch (Get-Random -Minimum 1 -Maximum 4) {
            1 { exit 1 }
            2 { [Environment]::Exit(1) }
            3 { throw "Security violation detected" }
            default { return }
        }
    }
    
    # 启动持续监控
    static [void] StartContinuousMonitoring() {
        Write-Host "[ANTI-DEBUG] Starting continuous monitoring..." -ForegroundColor Cyan
        
        # 创建后台作业进行持续监控
        $monitoringScript = {
            while ($true) {
                if ([AntiDebugProtection]::PerformAntiDebugCheck()) {
                    [AntiDebugProtection]::AntiDebugResponse()
                    break
                }
                Start-Sleep 5
            }
        }
        
        # 启动监控作业
        try {
            Start-Job -ScriptBlock $monitoringScript -Name "AntiDebugMonitor" | Out-Null
            Write-Host "[ANTI-DEBUG] Continuous monitoring started" -ForegroundColor Green
        } catch {
            Write-Host "[ANTI-DEBUG] Failed to start monitoring: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

# 生成受保护的代码
function Add-AntiDebugProtection {
    param(
        [Parameter(Mandatory=$true)]
        [string]$InputFile,
        [string]$OutputFile = "",
        [switch]$ContinuousMonitoring
    )
    
    Write-Host "🛡️ 添加反调试保护..." -ForegroundColor Cyan
    
    if (-not (Test-Path $InputFile)) {
        Write-Host "❌ 输入文件不存在: $InputFile" -ForegroundColor Red
        return $false
    }
    
    if ($OutputFile -eq "") {
        $OutputFile = $InputFile.Replace(".ps1", "-AntiDebug.ps1")
    }
    
    try {
        # 读取原始代码
        $originalCode = Get-Content $InputFile -Raw
        
        # 创建受保护的代码
        $protectedCode = @"
# Augment VIP Tool - Anti-Debug Protected Version
# This file is protected against debugging and reverse engineering

# Anti-Debug Protection Module
class AntiDebugProtection {
    static [bool] `$DebugDetected = `$false
    
    static [bool] IsDebuggerAttached() {
        return [System.Diagnostics.Debugger]::IsAttached
    }
    
    static [bool] IsRunningInVM() {
        try {
            `$bios = Get-WmiObject -Class Win32_BIOS
            return (`$bios.Manufacturer -match "VMware|VirtualBox|Microsoft Corporation|Xen|QEMU")
        } catch {
            return `$false
        }
    }
    
    static [bool] DetectAnalysisTools() {
        try {
            `$tools = @("ollydbg", "x64dbg", "windbg", "ida", "processhacker", "procmon")
            `$processes = Get-Process | ForEach-Object { `$_.ProcessName.ToLower() }
            return (`$tools | Where-Object { `$processes -contains `$_ }).Count -gt 0
        } catch {
            return `$false
        }
    }
    
    static [bool] PerformCheck() {
        if ([AntiDebugProtection]::IsDebuggerAttached() -or 
            [AntiDebugProtection]::IsRunningInVM() -or 
            [AntiDebugProtection]::DetectAnalysisTools()) {
            [AntiDebugProtection]::`$DebugDetected = `$true
            return `$true
        }
        return `$false
    }
    
    static [void] Response() {
        Write-Host "[SECURITY] Debugging attempt detected. Exiting..." -ForegroundColor Red
        Start-Sleep 2
        exit 1
    }
}

# Initial anti-debug check
if ([AntiDebugProtection]::PerformCheck()) {
    [AntiDebugProtection]::Response()
}

Write-Host "[SECURITY] Anti-debug protection active" -ForegroundColor Green

# Periodic checks during execution
`$checkTimer = New-Object System.Timers.Timer
`$checkTimer.Interval = 5000  # 5 seconds
`$checkTimer.Add_Elapsed({
    if ([AntiDebugProtection]::PerformCheck()) {
        [AntiDebugProtection]::Response()
    }
})
`$checkTimer.Start()

# Original code execution
try {
$originalCode
} finally {
    `$checkTimer.Stop()
    `$checkTimer.Dispose()
}
"@
        
        # 保存受保护的代码
        $protectedCode | Out-File $OutputFile -Encoding UTF8
        
        Write-Host "✅ 反调试保护已添加" -ForegroundColor Green
        Write-Host "   输入文件: $InputFile" -ForegroundColor White
        Write-Host "   输出文件: $OutputFile" -ForegroundColor White
        
        return $true
        
    } catch {
        Write-Host "❌ 添加反调试保护失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 使用示例
if ($MyInvocation.InvocationName -eq $MyInvocation.MyCommand.Name) {
    Write-Host "🛡️ AUGMENT VIP TOOL - 反调试保护模块" -ForegroundColor Cyan
    Write-Host "===================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "💡 使用方法:" -ForegroundColor Yellow
    Write-Host "   Add-AntiDebugProtection -InputFile 'script.ps1'" -ForegroundColor White
    Write-Host "   Add-AntiDebugProtection -InputFile 'script.ps1' -ContinuousMonitoring" -ForegroundColor White
    Write-Host ""
    Write-Host "🔍 测试反调试功能:" -ForegroundColor Yellow
    Write-Host "   [AntiDebugProtection]::PerformAntiDebugCheck()" -ForegroundColor White
}
