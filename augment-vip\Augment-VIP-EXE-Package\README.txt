﻿# Augment VIP - EXE Distribution Package

## 馃殌 One-Click Executable Tools

This package contains standalone executable files that require no installation or dependencies.

### 馃搧 Package Contents

- **AugmentVIP.exe** - Basic version (47 KB)
  - Simple GUI interface
  - Core functionality
  - Lightweight

- **AugmentVIP-Enhanced.exe** - Enhanced version (48 KB) 猸?RECOMMENDED
  - Modern GUI with better design
  - Enhanced error handling
  - Progress indicators
  - Detailed status messages
  - Automatic backup with timestamps

### 馃幆 How to Use

1. **Download and Extract** this package to any folder
2. **Double-click** either EXE file to run
3. **Follow the GUI prompts** to use the tool

### 鈿狅笍 Important Notes

- **Close VS Code** before running the tool
- **Run as Administrator** if you encounter permission issues
- **Restart VS Code** after modifications to take effect
- All operations create automatic backups
- For educational and research purposes only

### 馃敡 What the Tool Does

1. **Database Cleaning**
   - Removes Augment-related entries from VS Code database
   - Supports VS Code and VS Code Insiders
   - Creates automatic backups

2. **Telemetry ID Modification**
   - Generates random machine ID and device ID
   - Modifies VS Code telemetry identifiers
   - Creates configuration file backups

### 馃搵 System Requirements

- Windows 10/11
- PowerShell (usually pre-installed)
- VS Code installed

### 馃洜锔?Troubleshooting

**If the EXE doesn't start:**
1. Right-click and select "Run as administrator"
2. Check Windows Defender/antivirus settings
3. Ensure PowerShell is enabled on your system

**If you get security warnings:**
- This is normal for unsigned executables
- Click "More info" 鈫?"Run anyway" if you trust the source

### 馃帀 Recommended Usage

1. Use **AugmentVIP-Enhanced.exe** for the best experience
2. Close VS Code completely before running
3. Follow the on-screen instructions
4. Restart VS Code after completion

### 馃摓 Support

For issues or questions, refer to the original project:
https://github.com/azrilaiman2003/augment-vip

---
**Built with PS2EXE - Standalone Windows Executables**
