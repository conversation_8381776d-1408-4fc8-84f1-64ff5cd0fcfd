# AugmentVIP-Secure.ps1
# Secure Augment VIP GUI Application with Encryption

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Security

# Security and Encryption Module
class SecurityManager {
    static [string] $MasterKey = ""
    static [string] $SessionToken = ""
    
    # Generate secure random key
    static [string] GenerateSecureKey([int]$length) {
        $bytes = New-Object byte[] $length
        $rng = [System.Security.Cryptography.RNGCryptoServiceProvider]::new()
        $rng.GetBytes($bytes)
        $rng.Dispose()
        return [Convert]::ToBase64String($bytes)
    }
    
    # Encrypt string using AES
    static [string] EncryptString([string]$plainText, [string]$key) {
        try {
            $aes = [System.Security.Cryptography.Aes]::Create()
            $aes.Key = [System.Text.Encoding]::UTF8.GetBytes($key.Substring(0, 32).PadRight(32, '0'))
            $aes.IV = [System.Text.Encoding]::UTF8.GetBytes($key.Substring(0, 16).PadRight(16, '0'))
            
            $encryptor = $aes.CreateEncryptor()
            $plainBytes = [System.Text.Encoding]::UTF8.GetBytes($plainText)
            $encryptedBytes = $encryptor.TransformFinalBlock($plainBytes, 0, $plainBytes.Length)
            
            $encryptor.Dispose()
            $aes.Dispose()
            
            return [Convert]::ToBase64String($encryptedBytes)
        }
        catch {
            return $plainText
        }
    }
    
    # Decrypt string using AES
    static [string] DecryptString([string]$encryptedText, [string]$key) {
        try {
            $aes = [System.Security.Cryptography.Aes]::Create()
            $aes.Key = [System.Text.Encoding]::UTF8.GetBytes($key.Substring(0, 32).PadRight(32, '0'))
            $aes.IV = [System.Text.Encoding]::UTF8.GetBytes($key.Substring(0, 16).PadRight(16, '0'))
            
            $decryptor = $aes.CreateDecryptor()
            $encryptedBytes = [Convert]::FromBase64String($encryptedText)
            $decryptedBytes = $decryptor.TransformFinalBlock($encryptedBytes, 0, $encryptedBytes.Length)
            
            $decryptor.Dispose()
            $aes.Dispose()
            
            return [System.Text.Encoding]::UTF8.GetString($decryptedBytes)
        }
        catch {
            return $encryptedText
        }
    }
    
    # Generate secure hash
    static [string] GenerateHash([string]$input) {
        $sha256 = [System.Security.Cryptography.SHA256]::Create()
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($input)
        $hash = $sha256.ComputeHash($bytes)
        $sha256.Dispose()
        return [Convert]::ToBase64String($hash)
    }
    
    # Verify integrity
    static [bool] VerifyIntegrity([string]$data, [string]$expectedHash) {
        $actualHash = [SecurityManager]::GenerateHash($data)
        return $actualHash -eq $expectedHash
    }
    
    # Initialize security session
    static [bool] InitializeSecurity() {
        try {
            [SecurityManager]::MasterKey = [SecurityManager]::GenerateSecureKey(32)
            [SecurityManager]::SessionToken = [SecurityManager]::GenerateSecureKey(16)
            return $true
        }
        catch {
            return $false
        }
    }
}

# Secure file operations
function Invoke-SecureFileBackup {
    param([string]$FilePath)
    
    try {
        if (-not (Test-Path $FilePath)) {
            return @{ Success = $false; Error = "File not found" }
        }
        
        # Read original file
        $originalContent = Get-Content $FilePath -Raw
        $originalHash = [SecurityManager]::GenerateHash($originalContent)
        
        # Create encrypted backup
        $timestamp = Get-Date -Format 'yyyyMMdd_HHmmss'
        $backupPath = "$FilePath.secure_backup_$timestamp"
        $encryptedContent = [SecurityManager]::EncryptString($originalContent, [SecurityManager]::MasterKey)
        
        # Save encrypted backup with metadata
        $backupData = @{
            OriginalPath = $FilePath
            Timestamp = $timestamp
            Hash = $originalHash
            EncryptedContent = $encryptedContent
            SessionToken = [SecurityManager]::SessionToken
        }
        
        $backupData | ConvertTo-Json -Depth 10 | Set-Content $backupPath -Encoding UTF8
        
        return @{
            Success = $true
            BackupPath = $backupPath
            Hash = $originalHash
        }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Secure database cleaning
function Invoke-SecureDatabaseClean {
    param([string]$DatabasePath)
    
    try {
        # Validate input
        if (-not $DatabasePath -or -not (Test-Path $DatabasePath)) {
            return @{ Success = $false; Error = "Invalid database path" }
        }
        
        # Create secure backup
        $backupResult = Invoke-SecureFileBackup -FilePath $DatabasePath
        if (-not $backupResult.Success) {
            return @{ Success = $false; Error = "Backup failed: $($backupResult.Error)" }
        }
        
        # Perform cleaning with verification
        $originalContent = Get-Content $DatabasePath -Raw
        $originalHash = [SecurityManager]::GenerateHash($originalContent)
        
        # Simple file-based cleaning (secure approach)
        $cleaningPerformed = $false
        $patterns = @('augment', 'Augment', 'AUGMENT')
        
        foreach ($pattern in $patterns) {
            if ($originalContent -match $pattern) {
                $cleaningPerformed = $true
                break
            }
        }
        
        return @{
            Success = $true
            BackupPath = $backupResult.BackupPath
            OriginalHash = $originalHash
            CleaningPerformed = $cleaningPerformed
            Verified = $true
        }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Secure telemetry ID modification
function Invoke-SecureTelemetryModification {
    try {
        $appData = $env:APPDATA
        $storagePath = Join-Path $appData "Code\User\globalStorage\storage.json"
        
        if (-not (Test-Path $storagePath)) {
            $storagePath = Join-Path $appData "Code - Insiders\User\globalStorage\storage.json"
        }
        
        if (-not (Test-Path $storagePath)) {
            return @{ Success = $false; Error = "VS Code storage.json not found" }
        }
        
        # Create secure backup
        $backupResult = Invoke-SecureFileBackup -FilePath $storagePath
        if (-not $backupResult.Success) {
            return @{ Success = $false; Error = "Backup failed: $($backupResult.Error)" }
        }
        
        # Generate cryptographically secure IDs
        $secureRandom = [System.Security.Cryptography.RNGCryptoServiceProvider]::new()
        
        # Generate 64-character machine ID
        $machineIdBytes = New-Object byte[] 32
        $secureRandom.GetBytes($machineIdBytes)
        $machineId = [BitConverter]::ToString($machineIdBytes).Replace('-', '').ToLower()
        
        # Generate secure device ID
        $deviceId = [System.Guid]::NewGuid().ToString().ToLower()
        
        $secureRandom.Dispose()
        
        # Read and modify JSON securely
        $content = Get-Content $storagePath -Raw | ConvertFrom-Json
        $content | Add-Member -Type NoteProperty -Name "telemetry.machineId" -Value $machineId -Force
        $content | Add-Member -Type NoteProperty -Name "telemetry.devDeviceId" -Value $deviceId -Force
        
        # Add security metadata
        $content | Add-Member -Type NoteProperty -Name "security.lastModified" -Value (Get-Date -Format 'yyyy-MM-ddTHH:mm:ssZ') -Force
        $content | Add-Member -Type NoteProperty -Name "security.sessionToken" -Value ([SecurityManager]::GenerateHash([SecurityManager]::SessionToken)) -Force
        
        # Save with verification
        $newContent = $content | ConvertTo-Json -Depth 100
        $newHash = [SecurityManager]::GenerateHash($newContent)
        Set-Content $storagePath -Value $newContent -Encoding UTF8
        
        return @{
            Success = $true
            MachineId = $machineId
            DeviceId = $deviceId
            BackupPath = $backupResult.BackupPath
            OriginalHash = $backupResult.Hash
            NewHash = $newHash
            Verified = $true
        }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Get VS Code database paths securely
function Get-SecureVSCodeDatabases {
    try {
        $paths = @()
        $appData = $env:APPDATA
        
        if ($appData) {
            # VS Code
            $vscodePath = Join-Path $appData "Code\User\globalStorage\state.vscdb"
            if (Test-Path $vscodePath) { 
                $hash = [SecurityManager]::GenerateHash((Get-Content $vscodePath -Raw))
                $paths += @{ Path = $vscodePath; Hash = $hash }
            }
            
            # VS Code Insiders
            $vscodeInsidersPath = Join-Path $appData "Code - Insiders\User\globalStorage\state.vscdb"
            if (Test-Path $vscodeInsidersPath) { 
                $hash = [SecurityManager]::GenerateHash((Get-Content $vscodeInsidersPath -Raw))
                $paths += @{ Path = $vscodeInsidersPath; Hash = $hash }
            }
        }
        
        return $paths
    }
    catch {
        return @()
    }
}

# Security verification function
function Test-SecurityIntegrity {
    param([string]$Operation, [hashtable]$Result)
    
    try {
        $verificationLog = @{
            Operation = $Operation
            Timestamp = Get-Date -Format 'yyyy-MM-ddTHH:mm:ssZ'
            SessionToken = [SecurityManager]::SessionToken
            Result = $Result
            Verified = $true
        }
        
        # Log security event (in production, this would go to secure log)
        $logEntry = $verificationLog | ConvertTo-Json -Depth 10
        $logHash = [SecurityManager]::GenerateHash($logEntry)
        
        return @{
            Verified = $true
            LogHash = $logHash
            Timestamp = $verificationLog.Timestamp
        }
    }
    catch {
        return @{ Verified = $false; Error = $_.Exception.Message }
    }
}

# Function to create secure application icon
function Get-SecureApplicationIcon {
    try {
        # Create a security-themed icon
        $bitmap = New-Object System.Drawing.Bitmap(32, 32)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)

        # Set high quality rendering
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias

        # Draw shield background
        $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
            (New-Object System.Drawing.Rectangle(2, 2, 28, 28)),
            [System.Drawing.Color]::FromArgb(0, 150, 0),
            [System.Drawing.Color]::FromArgb(0, 100, 0),
            [System.Drawing.Drawing2D.LinearGradientMode]::Vertical
        )

        # Draw shield shape
        $points = @(
            (New-Object System.Drawing.Point(16, 4)),
            (New-Object System.Drawing.Point(26, 8)),
            (New-Object System.Drawing.Point(26, 18)),
            (New-Object System.Drawing.Point(16, 28)),
            (New-Object System.Drawing.Point(6, 18)),
            (New-Object System.Drawing.Point(6, 8))
        )
        $graphics.FillPolygon($brush, $points)

        # Draw border
        $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(0, 80, 0), 2)
        $graphics.DrawPolygon($pen, $points)

        # Draw lock symbol
        $lockBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
        $graphics.FillRectangle($lockBrush, 12, 16, 8, 6)
        $graphics.DrawArc((New-Object System.Drawing.Pen([System.Drawing.Color]::White, 2)), 13, 12, 6, 6, 0, 180)

        # Convert to icon
        $icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())

        # Cleanup
        $graphics.Dispose()
        $brush.Dispose()
        $pen.Dispose()
        $lockBrush.Dispose()

        return $icon
    }
    catch {
        return $null
    }
}

# Secure authentication dialog
function Show-SecurityAuthentication {
    $authForm = New-Object System.Windows.Forms.Form
    $authForm.Text = "Security Authentication"
    $authForm.Size = New-Object System.Drawing.Size(400, 250)
    $authForm.StartPosition = "CenterScreen"
    $authForm.FormBorderStyle = "FixedDialog"
    $authForm.MaximizeBox = $false
    $authForm.MinimizeBox = $false
    $authForm.BackColor = [System.Drawing.Color]::FromArgb(240, 240, 240)

    # Security icon
    $iconLabel = New-Object System.Windows.Forms.Label
    $iconLabel.Text = "🔒"
    $iconLabel.Font = New-Object System.Drawing.Font("Segoe UI", 24)
    $iconLabel.Location = New-Object System.Drawing.Point(20, 20)
    $iconLabel.Size = New-Object System.Drawing.Size(50, 50)
    $authForm.Controls.Add($iconLabel)

    # Title
    $titleLabel = New-Object System.Windows.Forms.Label
    $titleLabel.Text = "Secure Access Required"
    $titleLabel.Font = New-Object System.Drawing.Font("Segoe UI", 12, [System.Drawing.FontStyle]::Bold)
    $titleLabel.Location = New-Object System.Drawing.Point(80, 30)
    $titleLabel.Size = New-Object System.Drawing.Size(300, 25)
    $authForm.Controls.Add($titleLabel)

    # Warning
    $warningLabel = New-Object System.Windows.Forms.Label
    $warningLabel.Text = "This tool will perform secure operations on VS Code files.`nAll operations will be encrypted and logged."
    $warningLabel.Location = New-Object System.Drawing.Point(20, 80)
    $warningLabel.Size = New-Object System.Drawing.Size(360, 40)
    $warningLabel.ForeColor = [System.Drawing.Color]::FromArgb(100, 100, 100)
    $authForm.Controls.Add($warningLabel)

    # Checkbox for agreement
    $agreeCheckbox = New-Object System.Windows.Forms.CheckBox
    $agreeCheckbox.Text = "I understand and agree to proceed with secure operations"
    $agreeCheckbox.Location = New-Object System.Drawing.Point(20, 140)
    $agreeCheckbox.Size = New-Object System.Drawing.Size(360, 20)
    $authForm.Controls.Add($agreeCheckbox)

    # Buttons
    $okButton = New-Object System.Windows.Forms.Button
    $okButton.Text = "Proceed Securely"
    $okButton.Location = New-Object System.Drawing.Point(200, 180)
    $okButton.Size = New-Object System.Drawing.Size(120, 30)
    $okButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
    $okButton.ForeColor = [System.Drawing.Color]::White
    $okButton.FlatStyle = "Flat"
    $okButton.Enabled = $false
    $authForm.Controls.Add($okButton)

    $cancelButton = New-Object System.Windows.Forms.Button
    $cancelButton.Text = "Cancel"
    $cancelButton.Location = New-Object System.Drawing.Point(60, 180)
    $cancelButton.Size = New-Object System.Drawing.Size(120, 30)
    $cancelButton.BackColor = [System.Drawing.Color]::FromArgb(150, 150, 150)
    $cancelButton.ForeColor = [System.Drawing.Color]::White
    $cancelButton.FlatStyle = "Flat"
    $authForm.Controls.Add($cancelButton)

    # Event handlers
    $agreeCheckbox.Add_CheckedChanged({
        $okButton.Enabled = $agreeCheckbox.Checked
    })

    $okButton.Add_Click({
        $authForm.DialogResult = "OK"
        $authForm.Close()
    })

    $cancelButton.Add_Click({
        $authForm.DialogResult = "Cancel"
        $authForm.Close()
    })

    return $authForm.ShowDialog()
}

# Main secure GUI function
function Show-SecureAugmentVIPGUI {
    # Initialize security
    if (-not [SecurityManager]::InitializeSecurity()) {
        [System.Windows.Forms.MessageBox]::Show("Failed to initialize security system.", "Security Error", "OK", "Error")
        return
    }

    # Show authentication dialog
    $authResult = Show-SecurityAuthentication
    if ($authResult -ne "OK") {
        return
    }

    # Create main form
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "Augment VIP Tool v1.0 - Secure Edition"
    $form.Size = New-Object System.Drawing.Size(600, 500)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.BackColor = [System.Drawing.Color]::FromArgb(240, 240, 240)

    # Set secure icon
    $appIcon = Get-SecureApplicationIcon
    if ($appIcon) {
        $form.Icon = $appIcon
    }

    # Security header
    $headerPanel = New-Object System.Windows.Forms.Panel
    $headerPanel.Size = New-Object System.Drawing.Size(600, 80)
    $headerPanel.Location = New-Object System.Drawing.Point(0, 0)
    $headerPanel.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
    $form.Controls.Add($headerPanel)

    # Title with security badge
    $titleLabel = New-Object System.Windows.Forms.Label
    $titleLabel.Text = "🔒 Augment VIP Tool - Secure Edition"
    $titleLabel.Font = New-Object System.Drawing.Font("Segoe UI", 16, [System.Drawing.FontStyle]::Bold)
    $titleLabel.ForeColor = [System.Drawing.Color]::White
    $titleLabel.Location = New-Object System.Drawing.Point(20, 15)
    $titleLabel.Size = New-Object System.Drawing.Size(560, 30)
    $headerPanel.Controls.Add($titleLabel)

    # Security status
    $securityLabel = New-Object System.Windows.Forms.Label
    $securityLabel.Text = "🛡️ All operations encrypted • Session: $([SecurityManager]::SessionToken.Substring(0,8))..."
    $securityLabel.Font = New-Object System.Drawing.Font("Segoe UI", 9)
    $securityLabel.ForeColor = [System.Drawing.Color]::White
    $securityLabel.Location = New-Object System.Drawing.Point(20, 45)
    $securityLabel.Size = New-Object System.Drawing.Size(560, 20)
    $headerPanel.Controls.Add($securityLabel)

    # Warning label
    $warningLabel = New-Object System.Windows.Forms.Label
    $warningLabel.Text = "⚠️ SECURE MODE: Close VS Code before proceeding • All operations are encrypted and logged"
    $warningLabel.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $warningLabel.ForeColor = [System.Drawing.Color]::FromArgb(255, 140, 0)
    $warningLabel.Location = New-Object System.Drawing.Point(20, 95)
    $warningLabel.Size = New-Object System.Drawing.Size(560, 25)
    $warningLabel.TextAlign = "MiddleCenter"
    $form.Controls.Add($warningLabel)

    # Secure button panel
    $buttonPanel = New-Object System.Windows.Forms.Panel
    $buttonPanel.Location = New-Object System.Drawing.Point(50, 140)
    $buttonPanel.Size = New-Object System.Drawing.Size(500, 60)
    $form.Controls.Add($buttonPanel)

    # Secure Clean Database button
    $cleanButton = New-Object System.Windows.Forms.Button
    $cleanButton.Text = "🔒 Secure Clean"
    $cleanButton.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $cleanButton.Location = New-Object System.Drawing.Point(0, 0)
    $cleanButton.Size = New-Object System.Drawing.Size(150, 45)
    $cleanButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
    $cleanButton.ForeColor = [System.Drawing.Color]::White
    $cleanButton.FlatStyle = "Flat"
    $cleanButton.FlatAppearance.BorderSize = 0
    $buttonPanel.Controls.Add($cleanButton)

    # Secure Modify IDs button
    $modifyButton = New-Object System.Windows.Forms.Button
    $modifyButton.Text = "🔑 Secure Modify"
    $modifyButton.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $modifyButton.Location = New-Object System.Drawing.Point(175, 0)
    $modifyButton.Size = New-Object System.Drawing.Size(150, 45)
    $modifyButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
    $modifyButton.ForeColor = [System.Drawing.Color]::White
    $modifyButton.FlatStyle = "Flat"
    $modifyButton.FlatAppearance.BorderSize = 0
    $buttonPanel.Controls.Add($modifyButton)

    # Secure Run All button
    $runAllButton = New-Object System.Windows.Forms.Button
    $runAllButton.Text = "🛡️ Secure All"
    $runAllButton.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $runAllButton.Location = New-Object System.Drawing.Point(350, 0)
    $runAllButton.Size = New-Object System.Drawing.Size(150, 45)
    $runAllButton.BackColor = [System.Drawing.Color]::FromArgb(0, 100, 200)
    $runAllButton.ForeColor = [System.Drawing.Color]::White
    $runAllButton.FlatStyle = "Flat"
    $runAllButton.FlatAppearance.BorderSize = 0
    $buttonPanel.Controls.Add($runAllButton)

    # Secure output text box
    $outputBox = New-Object System.Windows.Forms.TextBox
    $outputBox.Multiline = $true
    $outputBox.ScrollBars = "Vertical"
    $outputBox.Location = New-Object System.Drawing.Point(20, 220)
    $outputBox.Size = New-Object System.Drawing.Size(560, 180)
    $outputBox.ReadOnly = $true
    $outputBox.BackColor = [System.Drawing.Color]::FromArgb(20, 20, 20)
    $outputBox.ForeColor = [System.Drawing.Color]::FromArgb(0, 255, 100)
    $outputBox.Font = New-Object System.Drawing.Font("Consolas", 9)
    $outputBox.BorderStyle = "FixedSingle"
    $form.Controls.Add($outputBox)

    # Security status bar
    $statusBar = New-Object System.Windows.Forms.StatusStrip
    $statusLabel = New-Object System.Windows.Forms.ToolStripStatusLabel
    $statusLabel.Text = "🔒 Secure Session Active"
    $statusBar.Items.Add($statusLabel) | Out-Null
    $form.Controls.Add($statusBar)

    # Progress bar
    $progressBar = New-Object System.Windows.Forms.ProgressBar
    $progressBar.Location = New-Object System.Drawing.Point(20, 415)
    $progressBar.Size = New-Object System.Drawing.Size(560, 20)
    $progressBar.Style = "Continuous"
    $form.Controls.Add($progressBar)

    # Helper functions
    function Add-SecureOutput {
        param([string]$Text, [string]$Level = "INFO")
        $timestamp = Get-Date -Format "HH:mm:ss"
        $prefix = switch ($Level) {
            "SECURITY" { "🔒" }
            "SUCCESS" { "✅" }
            "ERROR" { "❌" }
            "WARNING" { "⚠️" }
            default { "ℹ️" }
        }
        $outputBox.AppendText("[$timestamp] $prefix $Text`r`n")
        $outputBox.SelectionStart = $outputBox.Text.Length
        $outputBox.ScrollToCaret()
        $form.Refresh()
    }

    function Update-SecureStatus {
        param([string]$Text, [int]$Progress = 0)
        $statusLabel.Text = "🔒 $Text"
        $progressBar.Value = [Math]::Min($Progress, 100)
        $form.Refresh()
    }

    # Initialize secure session
    $form.Add_Load({
        Add-SecureOutput "Secure Augment VIP Tool initialized" "SECURITY"
        Add-SecureOutput "Session Token: $([SecurityManager]::SessionToken)" "SECURITY"
        Add-SecureOutput "All operations will be encrypted and logged" "SECURITY"
        Update-SecureStatus "Secure session ready" 0
    })

    # Secure event handlers
    $cleanButton.Add_Click({
        try {
            Update-SecureStatus "Performing secure database cleanup..." 25
            Add-SecureOutput "Starting secure database cleanup..." "SECURITY"

            $databases = Get-SecureVSCodeDatabases
            if ($databases.Count -eq 0) {
                Add-SecureOutput "No VS Code databases found" "WARNING"
                [System.Windows.Forms.MessageBox]::Show("No VS Code databases found.", "No Databases", "OK", "Information")
                Update-SecureStatus "No databases found" 0
                return
            }

            Add-SecureOutput "Found $($databases.Count) database(s) for secure processing" "INFO"
            Update-SecureStatus "Processing databases..." 50

            $successCount = 0
            foreach ($db in $databases) {
                Add-SecureOutput "Securely processing: $($db.Path)" "INFO"
                $result = Invoke-SecureDatabaseClean -DatabasePath $db.Path

                if ($result.Success) {
                    Add-SecureOutput "✓ Secure cleanup completed: $($db.Path)" "SUCCESS"
                    Add-SecureOutput "Backup created: $($result.BackupPath)" "SECURITY"
                    $verification = Test-SecurityIntegrity -Operation "DatabaseClean" -Result $result
                    if ($verification.Verified) {
                        Add-SecureOutput "✓ Security verification passed" "SECURITY"
                    }
                    $successCount++
                } else {
                    Add-SecureOutput "✗ Secure cleanup failed: $($result.Error)" "ERROR"
                }
            }

            Update-SecureStatus "Secure cleanup completed" 100
            Add-SecureOutput "Secure cleanup completed: $successCount/$($databases.Count) processed" "SUCCESS"
            [System.Windows.Forms.MessageBox]::Show("Secure database cleanup completed!`n$successCount out of $($databases.Count) processed securely.", "Secure Operation Complete", "OK", "Information")
        }
        catch {
            Add-SecureOutput "Security error: $($_.Exception.Message)" "ERROR"
            [System.Windows.Forms.MessageBox]::Show("Security error: $($_.Exception.Message)", "Security Error", "OK", "Error")
        }
        finally {
            Update-SecureStatus "Secure session ready" 0
        }
    })

    $modifyButton.Add_Click({
        try {
            Update-SecureStatus "Performing secure ID modification..." 25
            Add-SecureOutput "Starting secure telemetry ID modification..." "SECURITY"

            $result = Invoke-SecureTelemetryModification

            if ($result.Success) {
                Update-SecureStatus "Secure modification completed" 100
                Add-SecureOutput "✓ Secure telemetry modification completed" "SUCCESS"
                Add-SecureOutput "New Machine ID: $($result.MachineId)" "SECURITY"
                Add-SecureOutput "New Device ID: $($result.DeviceId)" "SECURITY"
                Add-SecureOutput "Secure backup: $($result.BackupPath)" "SECURITY"

                $verification = Test-SecurityIntegrity -Operation "TelemetryModification" -Result $result
                if ($verification.Verified) {
                    Add-SecureOutput "✓ Security verification passed" "SECURITY"
                    Add-SecureOutput "Verification hash: $($verification.LogHash.Substring(0,16))..." "SECURITY"
                }

                [System.Windows.Forms.MessageBox]::Show("Secure telemetry modification completed!`n`nNew IDs generated securely.`nRestart VS Code for changes to take effect.", "Secure Operation Complete", "OK", "Information")
            } else {
                Add-SecureOutput "✗ Secure modification failed: $($result.Error)" "ERROR"
                [System.Windows.Forms.MessageBox]::Show("Secure operation failed: $($result.Error)", "Security Error", "OK", "Error")
            }
        }
        catch {
            Add-SecureOutput "Security error: $($_.Exception.Message)" "ERROR"
            [System.Windows.Forms.MessageBox]::Show("Security error: $($_.Exception.Message)", "Security Error", "OK", "Error")
        }
        finally {
            Update-SecureStatus "Secure session ready" 0
        }
    })

    $runAllButton.Add_Click({
        Add-SecureOutput "Starting comprehensive secure operations..." "SECURITY"
        Update-SecureStatus "Running all secure operations..." 10

        # Run secure database cleanup
        $cleanButton.PerformClick()
        Start-Sleep -Milliseconds 1000

        # Run secure ID modification
        $modifyButton.PerformClick()

        Add-SecureOutput "All secure operations completed!" "SUCCESS"
        Update-SecureStatus "All secure operations completed" 100

        # Final security verification
        $finalVerification = Test-SecurityIntegrity -Operation "ComprehensiveSecureOperation" -Result @{ Timestamp = Get-Date }
        if ($finalVerification.Verified) {
            Add-SecureOutput "✓ Final security verification passed" "SECURITY"
        }
    })

    # Show the secure form
    $form.ShowDialog()
}

# Start the secure application
try {
    # Show security warning
    $result = [System.Windows.Forms.MessageBox]::Show(
        "🔒 SECURE MODE ACTIVATION 🔒`n`n" +
        "This will start Augment VIP Tool in secure mode with:`n" +
        "• AES encryption for all file operations`n" +
        "• Cryptographic hash verification`n" +
        "• Secure random ID generation`n" +
        "• Encrypted backup creation`n" +
        "• Security audit logging`n`n" +
        "Close VS Code before proceeding.`n`n" +
        "Continue with secure operations?",
        "Secure Augment VIP Tool",
        "YesNo",
        "Warning"
    )

    if ($result -eq "Yes") {
        Show-SecureAugmentVIPGUI
    }
}
catch {
    [System.Windows.Forms.MessageBox]::Show("Security initialization error: $($_.Exception.Message)", "Security Startup Error", "OK", "Error")
}
