# Create ICO file for Augment VIP Tool
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

function Create-SimpleIcon {
    param([string]$OutputPath)
    
    try {
        # Create a 32x32 bitmap
        $bitmap = New-Object System.Drawing.Bitmap(32, 32)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Set high quality rendering
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        $graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias
        
        # Draw background circle
        $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(0, 120, 215))
        $graphics.FillEllipse($brush, 2, 2, 28, 28)
        
        # Draw border
        $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(0, 60, 120), 2)
        $graphics.DrawEllipse($pen, 2, 2, 28, 28)
        
        # Draw "AV" text
        $font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
        $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
        $graphics.DrawString("AV", $font, $textBrush, 8, 10)
        
        # Save as PNG first (ICO creation is complex in PowerShell)
        $pngPath = $OutputPath -replace '\.ico$', '.png'
        $bitmap.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Try to convert to ICO using .NET
        try {
            $icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())
            $fileStream = [System.IO.File]::Create($OutputPath)
            $icon.Save($fileStream)
            $fileStream.Close()
            Write-Host "ICO file created: $OutputPath"
        }
        catch {
            Write-Host "ICO creation failed, PNG available: $pngPath"
        }
        
        # Cleanup
        $graphics.Dispose()
        $brush.Dispose()
        $pen.Dispose()
        $textBrush.Dispose()
        $font.Dispose()
        $bitmap.Dispose()
        
        return $true
    }
    catch {
        Write-Host "Error creating icon: $($_.Exception.Message)"
        return $false
    }
}

# Create the icon
$icoPath = Join-Path $PSScriptRoot "augment-vip.ico"
Create-SimpleIcon -OutputPath $icoPath
