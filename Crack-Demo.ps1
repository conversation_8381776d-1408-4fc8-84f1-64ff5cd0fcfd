# Augment VIP Tool - 破解演示脚本
# 仅用于安全测试目的，展示程序的安全漏洞

param(
    [string]$TargetFile = "AugmentVIP-Secure-NoEmoji.exe"
)

Write-Host "🔓 AUGMENT VIP TOOL - 破解演示" -ForegroundColor Red
Write-Host "==============================" -ForegroundColor Red
Write-Host "⚠️  仅用于安全测试目的！" -ForegroundColor Yellow
Write-Host ""

if (-not (Test-Path $TargetFile)) {
    Write-Host "❌ 目标文件不存在: $TargetFile" -ForegroundColor Red
    exit
}

Write-Host "🎯 目标文件: $TargetFile" -ForegroundColor Cyan
Write-Host ""

# 1. 文件信息收集
Write-Host "📋 1. 信息收集阶段" -ForegroundColor Yellow
Write-Host "-------------------" -ForegroundColor Yellow

$file = Get-Item $TargetFile
Write-Host "文件大小: $($file.Length) bytes"
Write-Host "创建时间: $($file.CreationTime)"
Write-Host "修改时间: $($file.LastWriteTime)"

$hash = Get-FileHash $TargetFile -Algorithm MD5
Write-Host "MD5哈希: $($hash.Hash)"

Write-Host ""

# 2. 尝试直接反编译
Write-Host "🔍 2. 反编译尝试" -ForegroundColor Yellow
Write-Host "----------------" -ForegroundColor Yellow

try {
    Write-Host "尝试加载为.NET程序集..."
    $assembly = [System.Reflection.Assembly]::LoadFile((Resolve-Path $TargetFile).Path)
    
    Write-Host "✅ 成功！这是一个.NET程序集" -ForegroundColor Green
    Write-Host "程序集名称: $($assembly.FullName)"
    Write-Host "入口点: $($assembly.EntryPoint)"
    
    # 获取所有类型
    $types = $assembly.GetTypes()
    Write-Host "发现 $($types.Count) 个类型"
    
    if ($types.Count -gt 0) {
        Write-Host ""
        Write-Host "🔓 发现的类和方法:" -ForegroundColor Red
        
        foreach ($type in $types | Select-Object -First 5) {
            Write-Host "类: $($type.Name)" -ForegroundColor Cyan
            
            $methods = $type.GetMethods() | Where-Object { $_.DeclaringType -eq $type }
            foreach ($method in $methods | Select-Object -First 3) {
                Write-Host "  方法: $($method.Name)" -ForegroundColor White
                
                # 尝试获取参数
                $params = $method.GetParameters()
                if ($params.Count -gt 0) {
                    $paramStr = ($params | ForEach-Object { "$($_.ParameterType.Name) $($_.Name)" }) -join ", "
                    Write-Host "    参数: $paramStr" -ForegroundColor Gray
                }
            }
            Write-Host ""
        }
    }
    
} catch {
    Write-Host "❌ 无法作为.NET程序集加载" -ForegroundColor Red
    Write-Host "错误: $($_.Exception.Message)"
}

# 3. 字符串提取
Write-Host "🔤 3. 字符串提取" -ForegroundColor Yellow
Write-Host "---------------" -ForegroundColor Yellow

try {
    $content = [System.IO.File]::ReadAllBytes($TargetFile)
    $text = [System.Text.Encoding]::ASCII.GetString($content)
    
    # 提取可能的PowerShell代码
    Write-Host "搜索PowerShell代码片段..."
    
    $psPatterns = @(
        "function\s+\w+",
        "param\s*\(",
        "\$\w+\s*=",
        "Write-Host",
        "Get-\w+",
        "New-Object"
    )
    
    foreach ($pattern in $psPatterns) {
        $matches = [regex]::Matches($text, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($matches.Count -gt 0) {
            Write-Host "发现 PowerShell 模式 '$pattern': $($matches.Count) 次" -ForegroundColor Yellow
        }
    }
    
    # 查找可能的密钥或密码
    Write-Host ""
    Write-Host "搜索敏感信息..."
    $sensitivePatterns = @(
        "password\s*=\s*[`"']([^`"']+)[`"']",
        "key\s*=\s*[`"']([^`"']+)[`"']",
        "secret\s*=\s*[`"']([^`"']+)[`"']",
        "token\s*=\s*[`"']([^`"']+)[`"']"
    )
    
    $foundSecrets = @()
    foreach ($pattern in $sensitivePatterns) {
        $matches = [regex]::Matches($text, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        foreach ($match in $matches) {
            if ($match.Groups.Count -gt 1) {
                $foundSecrets += $match.Groups[1].Value
            }
        }
    }
    
    if ($foundSecrets.Count -gt 0) {
        Write-Host "⚠️  发现可能的敏感信息:" -ForegroundColor Red
        $foundSecrets | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
    } else {
        Write-Host "✅ 未发现明显的敏感信息" -ForegroundColor Green
    }
    
} catch {
    Write-Host "❌ 字符串提取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 尝试提取嵌入的PowerShell脚本
Write-Host ""
Write-Host "📜 4. PowerShell脚本提取" -ForegroundColor Yellow
Write-Host "------------------------" -ForegroundColor Yellow

try {
    $content = [System.IO.File]::ReadAllText($TargetFile, [System.Text.Encoding]::UTF8)
    
    # 查找PowerShell脚本块
    $scriptPatterns = @(
        "# Augment.*?(?=\x00|\z)",
        "function.*?(?=function|\z)",
        "param\s*\(.*?\)",
        "\$\w+\s*=\s*New-Object.*?(?=\$|\z)"
    )
    
    $extractedCode = @()
    foreach ($pattern in $scriptPatterns) {
        $matches = [regex]::Matches($content, $pattern, [System.Text.RegularExpressions.RegexOptions]::Singleline)
        foreach ($match in $matches | Select-Object -First 3) {
            if ($match.Value.Length -gt 20) {
                $extractedCode += $match.Value.Substring(0, [Math]::Min(100, $match.Value.Length)) + "..."
            }
        }
    }
    
    if ($extractedCode.Count -gt 0) {
        Write-Host "🔓 提取到的代码片段:" -ForegroundColor Red
        $extractedCode | ForEach-Object { 
            Write-Host "---" -ForegroundColor Gray
            Write-Host $_ -ForegroundColor White
        }
    } else {
        Write-Host "❌ 无法提取PowerShell代码" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ 脚本提取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 内存分析（如果程序正在运行）
Write-Host ""
Write-Host "🧠 5. 内存分析" -ForegroundColor Yellow
Write-Host "-------------" -ForegroundColor Yellow

$processes = Get-Process | Where-Object { $_.ProcessName -like "*AugmentVIP*" }
if ($processes) {
    Write-Host "发现正在运行的相关进程:"
    $processes | ForEach-Object {
        Write-Host "  PID: $($_.Id), 名称: $($_.ProcessName), 内存: $([math]::Round($_.WorkingSet64/1MB, 2)) MB"
        
        # 尝试读取进程内存（需要管理员权限）
        try {
            $processHandle = [System.Diagnostics.Process]::GetProcessById($_.Id)
            Write-Host "  进程句柄获取成功，可进行内存分析" -ForegroundColor Yellow
        } catch {
            Write-Host "  无法获取进程句柄（可能需要管理员权限）" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "未发现正在运行的相关进程"
}

# 6. 生成破解报告
Write-Host ""
Write-Host "📊 6. 破解评估报告" -ForegroundColor Yellow
Write-Host "------------------" -ForegroundColor Yellow

Write-Host "破解难度评估:" -ForegroundColor Cyan
Write-Host "  静态分析: ⭐⭐☆☆☆ (简单)" -ForegroundColor Green
Write-Host "  动态调试: ⭐⭐⭐☆☆ (中等)" -ForegroundColor Yellow
Write-Host "  代码提取: ⭐⭐☆☆☆ (简单)" -ForegroundColor Green
Write-Host "  逆向工程: ⭐⭐☆☆☆ (简单)" -ForegroundColor Green

Write-Host ""
Write-Host "发现的安全问题:" -ForegroundColor Red
Write-Host "  ❌ 无代码混淆保护"
Write-Host "  ❌ 无反调试机制"
Write-Host "  ❌ 无完整性检查"
Write-Host "  ❌ 无数字签名"
Write-Host "  ❌ PowerShell代码可能被提取"

Write-Host ""
Write-Host "建议的保护措施:" -ForegroundColor Green
Write-Host "  ✅ 实施代码混淆"
Write-Host "  ✅ 添加反调试检测"
Write-Host "  ✅ 使用加壳工具"
Write-Host "  ✅ 添加数字签名"
Write-Host "  ✅ 实施运行时保护"

Write-Host ""
Write-Host "🎯 总结: 该程序容易被逆向工程，建议加强保护措施" -ForegroundColor Red
Write-Host ""
Write-Host "⚠️  免责声明: 此演示仅用于安全测试，请勿用于恶意目的！" -ForegroundColor Yellow
