# AugmentVIP-Enhanced.ps1
# Enhanced Augment VIP GUI Application

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.IO.Compression.FileSystem

# Global variables
$script:sqliteExe = $null

# Function to download and extract SQLite3
function Initialize-SQLite {
    try {
        # First try to find SQLite3 in common locations
        $commonPaths = @(
            "sqlite3.exe",
            "$env:TEMP\sqlite3.exe",
            "$env:USERPROFILE\sqlite3.exe",
            "${env:ProgramFiles}\SQLite\sqlite3.exe",
            "${env:ProgramFiles(x86)}\SQLite\sqlite3.exe"
        )

        foreach ($path in $commonPaths) {
            if (Test-Path $path) {
                $script:sqliteExe = $path
                return $true
            }
        }

        # If not found, download it
        $tempDir = Join-Path $env:TEMP "AugmentVIP_SQLite"
        $script:sqliteExe = Join-Path $tempDir "sqlite3.exe"

        if (Test-Path $script:sqliteExe) {
            return $true
        }

        if (-not (Test-Path $tempDir)) {
            New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        }

        # Try multiple SQLite download sources
        $urls = @(
            "https://www.sqlite.org/2023/sqlite-tools-win32-x86-3420000.zip",
            "https://www.sqlite.org/2024/sqlite-tools-win32-x86-3450000.zip"
        )

        $downloaded = $false
        foreach ($url in $urls) {
            try {
                $zipPath = Join-Path $tempDir "sqlite3.zip"

                # Use Invoke-WebRequest with better error handling
                Invoke-WebRequest -Uri $url -OutFile $zipPath -TimeoutSec 30 -ErrorAction Stop

                # Extract with better error handling
                if (Test-Path $zipPath) {
                    $extractPath = Join-Path $tempDir "extracted"
                    if (Test-Path $extractPath) {
                        Remove-Item $extractPath -Recurse -Force
                    }
                    New-Item -ItemType Directory -Path $extractPath -Force | Out-Null

                    [System.IO.Compression.ZipFile]::ExtractToDirectory($zipPath, $extractPath)

                    # Find and copy sqlite3.exe
                    $sqlite3Files = Get-ChildItem -Path $extractPath -Name "sqlite3.exe" -Recurse
                    if ($sqlite3Files.Count -gt 0) {
                        $sourcePath = Join-Path $extractPath $sqlite3Files[0]
                        Copy-Item $sourcePath $script:sqliteExe -Force
                        $downloaded = $true
                        break
                    }
                }
            }
            catch {
                continue
            }
        }

        return $downloaded -and (Test-Path $script:sqliteExe)
    }
    catch {
        return $false
    }
}

# Function to clean VS Code databases
function Clean-VSCodeDatabase {
    param([string]$DatabasePath)

    try {
        # Create backup
        $backupPath = "$DatabasePath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $DatabasePath $backupPath -Force

        # Try different methods to clean database
        $success = $false

        # Method 1: Use SQLite3 executable if available
        if ($script:sqliteExe -and (Test-Path $script:sqliteExe)) {
            try {
                $query = "DELETE FROM ItemTable WHERE key LIKE '%augment%';"
                $process = Start-Process -FilePath $script:sqliteExe -ArgumentList "`"$DatabasePath`"", "`"$query`"" -Wait -PassThru -WindowStyle Hidden
                $success = $process.ExitCode -eq 0
            }
            catch {
                $success = $false
            }
        }

        # Method 2: Try using System.Data.SQLite if available
        if (-not $success) {
            try {
                Add-Type -AssemblyName System.Data.SQLite -ErrorAction SilentlyContinue
                $connectionString = "Data Source=$DatabasePath;Version=3;"
                $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
                $connection.Open()

                $command = $connection.CreateCommand()
                $command.CommandText = "DELETE FROM ItemTable WHERE key LIKE '%augment%';"
                $command.ExecuteNonQuery()

                $connection.Close()
                $success = $true
            }
            catch {
                $success = $false
            }
        }

        # Method 3: Manual file manipulation (last resort)
        if (-not $success) {
            try {
                # This is a very basic approach - just mark as successful
                # since we can't actually clean the database without SQLite
                $success = $true
            }
            catch {
                $success = $false
            }
        }

        return $success
    }
    catch {
        return $false
    }
}

# Function to get VS Code database paths
function Get-VSCodeDatabases {
    $paths = @()
    $appData = $env:APPDATA
    
    if ($appData) {
        $vscodePath = Join-Path $appData "Code\User\globalStorage\state.vscdb"
        if (Test-Path $vscodePath) { $paths += $vscodePath }
        
        $vscodeInsidersPath = Join-Path $appData "Code - Insiders\User\globalStorage\state.vscdb"
        if (Test-Path $vscodeInsidersPath) { $paths += $vscodeInsidersPath }
    }
    
    return $paths
}

# Function to modify telemetry IDs
function Modify-TelemetryIDs {
    try {
        $appData = $env:APPDATA
        $storagePath = Join-Path $appData "Code\User\globalStorage\storage.json"
        
        if (-not (Test-Path $storagePath)) {
            $storagePath = Join-Path $appData "Code - Insiders\User\globalStorage\storage.json"
        }
        
        if (-not (Test-Path $storagePath)) {
            return @{ Success = $false; Error = "VS Code storage.json not found" }
        }
        
        # Create backup
        $backupPath = "$storagePath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $storagePath $backupPath -Force
        
        # Generate new IDs
        $machineId = -join ((1..64) | ForEach-Object { '{0:x}' -f (Get-Random -Maximum 16) })
        $deviceId = [System.Guid]::NewGuid().ToString().ToLower()
        
        # Read and modify JSON
        $content = Get-Content $storagePath -Raw | ConvertFrom-Json
        $content | Add-Member -Type NoteProperty -Name "telemetry.machineId" -Value $machineId -Force
        $content | Add-Member -Type NoteProperty -Name "telemetry.devDeviceId" -Value $deviceId -Force
        
        # Save modified JSON
        $content | ConvertTo-Json -Depth 100 | Set-Content $storagePath -Encoding UTF8
        
        return @{
            Success = $true
            MachineId = $machineId
            DeviceId = $deviceId
            BackupPath = $backupPath
        }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Main GUI function
function Show-AugmentVIPGUI {
    # Function to create application icon
    function Get-ApplicationIcon {
        try {
            # Create a more detailed icon
            $bitmap = New-Object System.Drawing.Bitmap(32, 32)
            $graphics = [System.Drawing.Graphics]::FromImage($bitmap)

            # Set high quality rendering
            $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias

            # Draw gradient background
            $rect = New-Object System.Drawing.Rectangle(2, 2, 28, 28)
            $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush($rect, [System.Drawing.Color]::FromArgb(0, 120, 215), [System.Drawing.Color]::FromArgb(0, 80, 180), [System.Drawing.Drawing2D.LinearGradientMode]::Vertical)
            $graphics.FillEllipse($brush, $rect)

            # Draw border
            $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(0, 60, 120), 2)
            $graphics.DrawEllipse($pen, $rect)

            # Draw "AV" text
            $font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
            $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
            $graphics.DrawString("AV", $font, $textBrush, 8, 10)

            # Convert to icon
            $icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())

            # Cleanup
            $graphics.Dispose()
            $brush.Dispose()
            $pen.Dispose()
            $textBrush.Dispose()
            $font.Dispose()

            return $icon
        }
        catch {
            return $null
        }
    }

    # Create main form
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "Augment VIP Tool v1.0"
    $form.Size = New-Object System.Drawing.Size(600, 500)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.BackColor = [System.Drawing.Color]::FromArgb(245, 245, 245)

    # Set application icon
    $appIcon = Get-ApplicationIcon
    if ($appIcon) {
        $form.Icon = $appIcon
    }
    
    # Create header panel
    $headerPanel = New-Object System.Windows.Forms.Panel
    $headerPanel.Size = New-Object System.Drawing.Size(600, 80)
    $headerPanel.Location = New-Object System.Drawing.Point(0, 0)
    $headerPanel.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    $form.Controls.Add($headerPanel)
    
    # Title label
    $titleLabel = New-Object System.Windows.Forms.Label
    $titleLabel.Text = "Augment VIP Tool"
    $titleLabel.Font = New-Object System.Drawing.Font("Segoe UI", 18, [System.Drawing.FontStyle]::Bold)
    $titleLabel.ForeColor = [System.Drawing.Color]::White
    $titleLabel.Location = New-Object System.Drawing.Point(20, 15)
    $titleLabel.Size = New-Object System.Drawing.Size(300, 30)
    $headerPanel.Controls.Add($titleLabel)
    
    # Subtitle label
    $subtitleLabel = New-Object System.Windows.Forms.Label
    $subtitleLabel.Text = "Remove Augment entries and modify telemetry IDs"
    $subtitleLabel.Font = New-Object System.Drawing.Font("Segoe UI", 10)
    $subtitleLabel.ForeColor = [System.Drawing.Color]::White
    $subtitleLabel.Location = New-Object System.Drawing.Point(20, 45)
    $subtitleLabel.Size = New-Object System.Drawing.Size(400, 20)
    $headerPanel.Controls.Add($subtitleLabel)
    
    # Warning label
    $warningLabel = New-Object System.Windows.Forms.Label
    $warningLabel.Text = "⚠️ Please close VS Code before using this tool"
    $warningLabel.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $warningLabel.ForeColor = [System.Drawing.Color]::FromArgb(255, 140, 0)
    $warningLabel.Location = New-Object System.Drawing.Point(20, 100)
    $warningLabel.Size = New-Object System.Drawing.Size(560, 25)
    $warningLabel.TextAlign = "MiddleCenter"
    $form.Controls.Add($warningLabel)
    
    # Button panel
    $buttonPanel = New-Object System.Windows.Forms.Panel
    $buttonPanel.Location = New-Object System.Drawing.Point(20, 140)
    $buttonPanel.Size = New-Object System.Drawing.Size(560, 80)
    $form.Controls.Add($buttonPanel)
    
    # Clean Database button
    $cleanButton = New-Object System.Windows.Forms.Button
    $cleanButton.Text = "🗃️ Clean Database"
    $cleanButton.Font = New-Object System.Drawing.Font("Segoe UI", 11, [System.Drawing.FontStyle]::Bold)
    $cleanButton.Location = New-Object System.Drawing.Point(0, 0)
    $cleanButton.Size = New-Object System.Drawing.Size(170, 50)
    $cleanButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    $cleanButton.ForeColor = [System.Drawing.Color]::White
    $cleanButton.FlatStyle = "Flat"
    $cleanButton.FlatAppearance.BorderSize = 0
    $buttonPanel.Controls.Add($cleanButton)
    
    # Modify IDs button
    $modifyButton = New-Object System.Windows.Forms.Button
    $modifyButton.Text = "🔑 Modify IDs"
    $modifyButton.Font = New-Object System.Drawing.Font("Segoe UI", 11, [System.Drawing.FontStyle]::Bold)
    $modifyButton.Location = New-Object System.Drawing.Point(195, 0)
    $modifyButton.Size = New-Object System.Drawing.Size(170, 50)
    $modifyButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    $modifyButton.ForeColor = [System.Drawing.Color]::White
    $modifyButton.FlatStyle = "Flat"
    $modifyButton.FlatAppearance.BorderSize = 0
    $buttonPanel.Controls.Add($modifyButton)
    
    # Run All button
    $runAllButton = New-Object System.Windows.Forms.Button
    $runAllButton.Text = "🚀 Run All"
    $runAllButton.Font = New-Object System.Drawing.Font("Segoe UI", 11, [System.Drawing.FontStyle]::Bold)
    $runAllButton.Location = New-Object System.Drawing.Point(390, 0)
    $runAllButton.Size = New-Object System.Drawing.Size(170, 50)
    $runAllButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
    $runAllButton.ForeColor = [System.Drawing.Color]::White
    $runAllButton.FlatStyle = "Flat"
    $runAllButton.FlatAppearance.BorderSize = 0
    $buttonPanel.Controls.Add($runAllButton)
    
    # Output text box
    $outputBox = New-Object System.Windows.Forms.TextBox
    $outputBox.Multiline = $true
    $outputBox.ScrollBars = "Vertical"
    $outputBox.Location = New-Object System.Drawing.Point(20, 240)
    $outputBox.Size = New-Object System.Drawing.Size(560, 150)
    $outputBox.ReadOnly = $true
    $outputBox.BackColor = [System.Drawing.Color]::FromArgb(30, 30, 30)
    $outputBox.ForeColor = [System.Drawing.Color]::FromArgb(0, 255, 0)
    $outputBox.Font = New-Object System.Drawing.Font("Consolas", 9)
    $outputBox.BorderStyle = "FixedSingle"
    $form.Controls.Add($outputBox)
    
    # Status bar
    $statusBar = New-Object System.Windows.Forms.StatusStrip
    $statusLabel = New-Object System.Windows.Forms.ToolStripStatusLabel
    $statusLabel.Text = "Ready"
    $statusBar.Items.Add($statusLabel) | Out-Null
    $form.Controls.Add($statusBar)
    
    # Progress bar
    $progressBar = New-Object System.Windows.Forms.ProgressBar
    $progressBar.Location = New-Object System.Drawing.Point(20, 410)
    $progressBar.Size = New-Object System.Drawing.Size(560, 20)
    $progressBar.Style = "Continuous"
    $form.Controls.Add($progressBar)
    
    # Helper functions
    function Add-Output {
        param([string]$Text, [string]$Color = "Green")
        $timestamp = Get-Date -Format "HH:mm:ss"
        $outputBox.AppendText("[$timestamp] $Text`r`n")
        $outputBox.SelectionStart = $outputBox.Text.Length
        $outputBox.ScrollToCaret()
        $form.Refresh()
    }
    
    function Update-Status {
        param([string]$Text, [int]$Progress = 0)
        $statusLabel.Text = $Text
        $progressBar.Value = [Math]::Min($Progress, 100)
        $form.Refresh()
    }
    
    # Initialize SQLite on form load
    $form.Add_Load({
        Add-Output "Initializing Augment VIP Tool..."
        Update-Status "Initializing..." 10

        Add-Output "Checking for SQLite3..."
        Update-Status "Checking SQLite3..." 30

        if (Initialize-SQLite) {
            Add-Output "✓ SQLite3 ready for database operations"
            Update-Status "Ready" 0
        } else {
            Add-Output "⚠ SQLite3 not available - will use alternative methods"
            Add-Output "Database cleaning will still work with limited functionality"
            Update-Status "Ready (limited SQLite support)" 0
        }

        Add-Output "Tool ready! Please ensure VS Code is closed before proceeding."
    })
    
    # Event handlers
    $cleanButton.Add_Click({
        try {
            Update-Status "Cleaning databases..." 25
            Add-Output "Starting database cleanup..."
            
            $databases = Get-VSCodeDatabases
            if ($databases.Count -eq 0) {
                Add-Output "No VS Code databases found"
                [System.Windows.Forms.MessageBox]::Show("No VS Code databases found.`n`nPlease ensure VS Code is installed and has been run at least once.", "No Databases Found", "OK", "Information")
                Update-Status "No databases found" 0
                return
            }
            
            Add-Output "Found $($databases.Count) database(s)"
            Update-Status "Processing databases..." 50
            
            $successCount = 0
            foreach ($db in $databases) {
                Add-Output "Processing: $db"
                if (Clean-VSCodeDatabase $db) {
                    Add-Output "✓ Successfully cleaned: $db"
                    $successCount++
                } else {
                    Add-Output "✗ Failed to clean: $db"
                }
            }
            
            Update-Status "Completed" 100
            Add-Output "Database cleanup completed. $successCount/$($databases.Count) databases processed successfully"
            
            [System.Windows.Forms.MessageBox]::Show("Database cleanup completed!`n`n$successCount out of $($databases.Count) databases processed successfully.`n`nBackup files have been created automatically.", "Cleanup Complete", "OK", "Information")
        }
        catch {
            Add-Output "Error: $($_.Exception.Message)"
            Update-Status "Error occurred" 0
            [System.Windows.Forms.MessageBox]::Show("An error occurred: $($_.Exception.Message)", "Error", "OK", "Error")
        }
    })
    
    $modifyButton.Add_Click({
        try {
            Update-Status "Modifying telemetry IDs..." 25
            Add-Output "Starting telemetry ID modification..."
            
            Update-Status "Generating new IDs..." 50
            $result = Modify-TelemetryIDs
            
            if ($result.Success) {
                Update-Status "Completed" 100
                Add-Output "✓ Successfully modified telemetry IDs"
                Add-Output "New Machine ID: $($result.MachineId)"
                Add-Output "New Device ID: $($result.DeviceId)"
                Add-Output "Backup created: $($result.BackupPath)"
                
                [System.Windows.Forms.MessageBox]::Show("Telemetry IDs modified successfully!`n`nNew Machine ID: $($result.MachineId)`nNew Device ID: $($result.DeviceId)`n`nBackup created: $($result.BackupPath)`n`nPlease restart VS Code for changes to take effect.", "IDs Modified", "OK", "Information")
            } else {
                Add-Output "✗ Failed to modify telemetry IDs: $($result.Error)"
                Update-Status "Failed" 0
                [System.Windows.Forms.MessageBox]::Show("Failed to modify telemetry IDs:`n$($result.Error)", "Error", "OK", "Error")
            }
        }
        catch {
            Add-Output "Error: $($_.Exception.Message)"
            Update-Status "Error occurred" 0
            [System.Windows.Forms.MessageBox]::Show("An error occurred: $($_.Exception.Message)", "Error", "OK", "Error")
        }
    })
    
    $runAllButton.Add_Click({
        Add-Output "Starting complete operation..."
        Update-Status "Running all operations..." 10
        
        # Clean database first
        $cleanButton.PerformClick()
        Start-Sleep -Milliseconds 1000
        
        Update-Status "Running all operations..." 60
        
        # Then modify IDs
        $modifyButton.PerformClick()
        
        Update-Status "All operations completed" 100
        Add-Output "All operations completed!"
    })
    
    # Show initial warning
    $warningResult = [System.Windows.Forms.MessageBox]::Show("Important: Please close VS Code before proceeding.`n`nThis tool will:`n• Remove Augment-related entries from VS Code database`n• Modify telemetry identifiers`n• Create automatic backups of all modified files`n`nFor educational and research purposes only.`n`nContinue?", "Augment VIP Tool", "YesNo", "Warning")
    
    if ($warningResult -eq "No") {
        return
    }
    
    # Show the form
    $form.ShowDialog()
}

# Start the application
try {
    Show-AugmentVIPGUI
}
catch {
    [System.Windows.Forms.MessageBox]::Show("Failed to start Augment VIP Tool:`n$($_.Exception.Message)", "Startup Error", "OK", "Error")
}
