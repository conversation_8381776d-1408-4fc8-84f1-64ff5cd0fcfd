# Create Secure Icon for Augment VIP Tool
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

function Create-SecureIcon {
    param([string]$OutputPath)
    
    try {
        # Create a 32x32 bitmap
        $bitmap = New-Object System.Drawing.Bitmap(32, 32)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Set high quality rendering
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        $graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias
        
        # Draw shield background with gradient
        $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
            (New-Object System.Drawing.Rectangle(2, 2, 28, 28)),
            [System.Drawing.Color]::FromArgb(0, 150, 0),
            [System.Drawing.Color]::FromArgb(0, 100, 0),
            [System.Drawing.Drawing2D.LinearGradientMode]::Vertical
        )
        
        # Draw shield shape
        $points = @(
            (New-Object System.Drawing.Point(16, 4)),
            (New-Object System.Drawing.Point(26, 8)),
            (New-Object System.Drawing.Point(26, 18)),
            (New-Object System.Drawing.Point(16, 28)),
            (New-Object System.Drawing.Point(6, 18)),
            (New-Object System.Drawing.Point(6, 8))
        )
        $graphics.FillPolygon($brush, $points)
        
        # Draw border
        $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(0, 80, 0), 2)
        $graphics.DrawPolygon($pen, $points)
        
        # Draw lock symbol
        $lockBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
        # Lock body
        $graphics.FillRectangle($lockBrush, 12, 16, 8, 6)
        # Lock shackle
        $lockPen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 2)
        $graphics.DrawArc($lockPen, 13, 12, 6, 6, 0, 180)
        
        # Save as PNG first
        $pngPath = $OutputPath -replace '\.ico$', '.png'
        $bitmap.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Try to convert to ICO
        try {
            $icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())
            $fileStream = [System.IO.File]::Create($OutputPath)
            $icon.Save($fileStream)
            $fileStream.Close()
            Write-Host "Secure ICO file created: $OutputPath"
        }
        catch {
            Write-Host "ICO creation failed, PNG available: $pngPath"
        }
        
        # Cleanup
        $graphics.Dispose()
        $brush.Dispose()
        $pen.Dispose()
        $lockBrush.Dispose()
        $lockPen.Dispose()
        $bitmap.Dispose()
        
        return $true
    }
    catch {
        Write-Host "Error creating secure icon: $($_.Exception.Message)"
        return $false
    }
}

# Create the secure icon
$secureIconPath = Join-Path $PSScriptRoot "augment-vip-secure.ico"
Create-SecureIcon -OutputPath $secureIconPath
