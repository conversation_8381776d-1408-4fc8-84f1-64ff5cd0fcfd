# Create-Final-Package.ps1
# Create the final distribution package with EXE files

param(
    [string]$OutputPath = ".\Augment-VIP-EXE-Package"
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    switch ($Level) {
        "INFO" { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Blue }
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green }
        "WARNING" { Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor Yellow }
        "ERROR" { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red }
    }
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Augment VIP EXE Package Creator    " -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Log "Creating final EXE distribution package..." "INFO"

# Clean and create output directory
if (Test-Path $OutputPath) {
    Remove-Item $OutputPath -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
Write-Log "Created output directory: $OutputPath" "SUCCESS"

# Copy EXE files
$exeFiles = @(
    "AugmentVIP.exe",
    "AugmentVIP-Enhanced.exe"
)

foreach ($exe in $exeFiles) {
    if (Test-Path $exe) {
        Copy-Item $exe (Join-Path $OutputPath $exe) -Force
        $size = (Get-Item $exe).Length
        Write-Log "Copied: $exe ($([math]::Round($size/1KB, 1)) KB)" "SUCCESS"
    } else {
        Write-Log "EXE file not found: $exe" "WARNING"
    }
}

# Create README for EXE package
$readmeContent = @'
# Augment VIP - EXE Distribution Package

## 🚀 One-Click Executable Tools

This package contains standalone executable files that require no installation or dependencies.

### 📁 Package Contents

- **AugmentVIP.exe** - Basic version (47 KB)
  - Simple GUI interface
  - Core functionality
  - Lightweight

- **AugmentVIP-Enhanced.exe** - Enhanced version (48 KB) ⭐ RECOMMENDED
  - Modern GUI with better design
  - Enhanced error handling
  - Progress indicators
  - Detailed status messages
  - Automatic backup with timestamps

### 🎯 How to Use

1. **Download and Extract** this package to any folder
2. **Double-click** either EXE file to run
3. **Follow the GUI prompts** to use the tool

### ⚠️ Important Notes

- **Close VS Code** before running the tool
- **Run as Administrator** if you encounter permission issues
- **Restart VS Code** after modifications to take effect
- All operations create automatic backups
- For educational and research purposes only

### 🔧 What the Tool Does

1. **Database Cleaning**
   - Removes Augment-related entries from VS Code database
   - Supports VS Code and VS Code Insiders
   - Creates automatic backups

2. **Telemetry ID Modification**
   - Generates random machine ID and device ID
   - Modifies VS Code telemetry identifiers
   - Creates configuration file backups

### 📋 System Requirements

- Windows 10/11
- PowerShell (usually pre-installed)
- VS Code installed

### 🛠️ Troubleshooting

**If the EXE doesn't start:**
1. Right-click and select "Run as administrator"
2. Check Windows Defender/antivirus settings
3. Ensure PowerShell is enabled on your system

**If you get security warnings:**
- This is normal for unsigned executables
- Click "More info" → "Run anyway" if you trust the source

### 🎉 Recommended Usage

1. Use **AugmentVIP-Enhanced.exe** for the best experience
2. Close VS Code completely before running
3. Follow the on-screen instructions
4. Restart VS Code after completion

### 📞 Support

For issues or questions, refer to the original project:
https://github.com/azrilaiman2003/augment-vip

---
**Built with PS2EXE - Standalone Windows Executables**
'@

$readmeContent | Set-Content (Join-Path $OutputPath "README.txt") -Encoding UTF8
Write-Log "Created README.txt" "SUCCESS"

# Create quick start guide
$quickStartContent = @'
QUICK START GUIDE
================

1. Double-click "AugmentVIP-Enhanced.exe" (recommended)
   OR
   Double-click "AugmentVIP.exe" (basic version)

2. Click "Yes" when prompted about closing VS Code

3. Choose your action:
   - "Clean Database" - Remove Augment entries
   - "Modify IDs" - Change telemetry identifiers  
   - "Run All" - Do both operations

4. Wait for completion message

5. Restart VS Code

IMPORTANT: Close VS Code before running!

For detailed help, see README.txt
'@

$quickStartContent | Set-Content (Join-Path $OutputPath "QUICK-START.txt") -Encoding ASCII
Write-Log "Created QUICK-START.txt" "SUCCESS"

# Create version info
$versionInfo = @{
    version = "1.0.0"
    build_date = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    platform = "Windows"
    type = "Standalone EXE"
    files = @{
        "AugmentVIP.exe" = "Basic version"
        "AugmentVIP-Enhanced.exe" = "Enhanced version (recommended)"
    }
    features = @(
        "GUI interface",
        "Database cleaning", 
        "Telemetry ID modification",
        "Automatic backups",
        "No installation required"
    )
} | ConvertTo-Json -Depth 3

$versionInfo | Set-Content (Join-Path $OutputPath "version.json") -Encoding UTF8
Write-Log "Created version.json" "SUCCESS"

# Create ZIP archive
$zipPath = "$OutputPath.zip"
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
}

try {
    Compress-Archive -Path "$OutputPath\*" -DestinationPath $zipPath -Force
    Write-Log "Created ZIP archive: $zipPath" "SUCCESS"
} catch {
    Write-Log "Failed to create ZIP: $($_.Exception.Message)" "ERROR"
}

# Calculate sizes
$packageSize = (Get-ChildItem $OutputPath -Recurse | Measure-Object -Property Length -Sum).Sum
$zipSize = if (Test-Path $zipPath) { (Get-Item $zipPath).Length } else { 0 }

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    EXE Package Creation Complete!     " -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Log "Package directory: $OutputPath" "INFO"
Write-Log "Package size: $([math]::Round($packageSize/1KB, 1)) KB" "INFO"
if ($zipSize -gt 0) {
    Write-Log "ZIP file: $zipPath" "INFO"
    Write-Log "ZIP size: $([math]::Round($zipSize/1KB, 1)) KB" "INFO"
}
Write-Host ""
Write-Host "🎉 Ready for Distribution!" -ForegroundColor Green
Write-Host ""
Write-Host "Distribution options:" -ForegroundColor Yellow
Write-Host "1. Share the ZIP file: $zipPath" -ForegroundColor Yellow
Write-Host "2. Share the folder: $OutputPath" -ForegroundColor Yellow
Write-Host "3. Users just extract and double-click the EXE!" -ForegroundColor Yellow
Write-Host ""
Write-Host "Recommended: AugmentVIP-Enhanced.exe (better UI)" -ForegroundColor Cyan
