# Augment VIP Tool - Security Testing Suite
# 测试打包程序的安全性

param(
    [string]$TargetFile = "augment-vip\AugmentVIP-Secure-NoEmoji.exe",
    [switch]$DetailedAnalysis,
    [switch]$ReverseEngineering,
    [switch]$MemoryAnalysis
)

Write-Host "🔍 AUGMENT VIP TOOL - SECURITY TESTING SUITE" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# 检查目标文件是否存在
if (-not (Test-Path $TargetFile)) {
    Write-Host "❌ Target file not found: $TargetFile" -ForegroundColor Red
    exit 1
}

Write-Host "🎯 Target: $TargetFile" -ForegroundColor Green
Write-Host ""

# 1. 基本文件信息分析
function Test-FileBasics {
    Write-Host "📋 1. BASIC FILE ANALYSIS" -ForegroundColor Yellow
    Write-Host "------------------------" -ForegroundColor Yellow
    
    $file = Get-Item $TargetFile
    Write-Host "File Size: $($file.Length) bytes"
    Write-Host "Created: $($file.CreationTime)"
    Write-Host "Modified: $($file.LastWriteTime)"
    Write-Host "Attributes: $($file.Attributes)"
    
    # 文件哈希
    $hash = Get-FileHash $TargetFile -Algorithm SHA256
    Write-Host "SHA256: $($hash.Hash)"
    
    # 数字签名检查
    try {
        $signature = Get-AuthenticodeSignature $TargetFile
        Write-Host "Digital Signature: $($signature.Status)"
        if ($signature.SignerCertificate) {
            Write-Host "Signer: $($signature.SignerCertificate.Subject)"
        }
    } catch {
        Write-Host "Digital Signature: Not signed"
    }
    Write-Host ""
}

# 2. 字符串分析 - 查找敏感信息
function Test-StringAnalysis {
    Write-Host "🔤 2. STRING ANALYSIS" -ForegroundColor Yellow
    Write-Host "--------------------" -ForegroundColor Yellow
    
    try {
        # 读取文件内容并查找可读字符串
        $content = [System.IO.File]::ReadAllBytes($TargetFile)
        $text = [System.Text.Encoding]::ASCII.GetString($content)
        
        # 查找敏感关键词
        $sensitivePatterns = @(
            "password", "secret", "key", "token", "api",
            "admin", "root", "database", "connection",
            "encrypt", "decrypt", "hash", "salt"
        )
        
        Write-Host "Searching for sensitive strings..."
        foreach ($pattern in $sensitivePatterns) {
            $matches = [regex]::Matches($text, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            if ($matches.Count -gt 0) {
                Write-Host "⚠️  Found '$pattern': $($matches.Count) occurrences" -ForegroundColor Yellow
            }
        }
        
        # 查找硬编码路径
        $pathMatches = [regex]::Matches($text, "[C-Z]:\\[^`"<>|]*", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($pathMatches.Count -gt 0) {
            Write-Host "📁 Found potential hardcoded paths: $($pathMatches.Count)"
            if ($DetailedAnalysis) {
                $pathMatches | Select-Object -First 5 | ForEach-Object { Write-Host "   $($_.Value)" }
            }
        }
        
        # 查找URL/域名
        $urlMatches = [regex]::Matches($text, "https?://[^\s`"<>]+", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        if ($urlMatches.Count -gt 0) {
            Write-Host "🌐 Found URLs: $($urlMatches.Count)"
            if ($DetailedAnalysis) {
                $urlMatches | Select-Object -First 3 | ForEach-Object { Write-Host "   $($_.Value)" }
            }
        }
        
    } catch {
        Write-Host "❌ String analysis failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    Write-Host ""
}

# 3. PE文件结构分析
function Test-PEStructure {
    Write-Host "🏗️  3. PE STRUCTURE ANALYSIS" -ForegroundColor Yellow
    Write-Host "---------------------------" -ForegroundColor Yellow
    
    try {
        # 使用.NET反射分析PE文件
        $assembly = [System.Reflection.Assembly]::LoadFile((Resolve-Path $TargetFile).Path)
        
        Write-Host "Assembly Name: $($assembly.FullName)"
        Write-Host "Entry Point: $($assembly.EntryPoint)"
        Write-Host "Location: $($assembly.Location)"
        
        # 获取模块信息
        $modules = $assembly.GetModules()
        Write-Host "Modules: $($modules.Count)"
        
        # 获取类型信息
        $types = $assembly.GetTypes()
        Write-Host "Types: $($types.Count)"
        
        if ($DetailedAnalysis) {
            Write-Host "Type Details:"
            $types | Select-Object -First 5 | ForEach-Object {
                Write-Host "   $($_.FullName)"
            }
        }
        
    } catch {
        Write-Host "⚠️  PE analysis failed (may not be .NET assembly): $($_.Exception.Message)" -ForegroundColor Yellow
        
        # 尝试基本PE头分析
        try {
            $bytes = [System.IO.File]::ReadAllBytes($TargetFile)
            if ($bytes.Length -gt 64) {
                $dosHeader = [System.BitConverter]::ToString($bytes[0..1])
                Write-Host "DOS Header: $dosHeader"
                
                if ($dosHeader -eq "4D-5A") {  # "MZ"
                    Write-Host "✅ Valid PE file (MZ signature found)"
                } else {
                    Write-Host "❌ Invalid PE file signature"
                }
            }
        } catch {
            Write-Host "❌ Basic PE analysis also failed"
        }
    }
    Write-Host ""
}

# 4. 依赖项分析
function Test-Dependencies {
    Write-Host "📦 4. DEPENDENCY ANALYSIS" -ForegroundColor Yellow
    Write-Host "------------------------" -ForegroundColor Yellow
    
    try {
        # 检查导入的DLL
        $content = [System.IO.File]::ReadAllText($TargetFile, [System.Text.Encoding]::ASCII)
        
        $commonDlls = @(
            "kernel32.dll", "user32.dll", "advapi32.dll", "shell32.dll",
            "ole32.dll", "oleaut32.dll", "msvcrt.dll", "ntdll.dll",
            "System.Windows.Forms.dll", "System.Drawing.dll"
        )
        
        Write-Host "Checking for common DLL dependencies..."
        foreach ($dll in $commonDlls) {
            if ($content -match [regex]::Escape($dll)) {
                Write-Host "✅ $dll" -ForegroundColor Green
            }
        }
        
    } catch {
        Write-Host "❌ Dependency analysis failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    Write-Host ""
}

# 5. 安全漏洞检查
function Test-SecurityVulnerabilities {
    Write-Host "🛡️  5. SECURITY VULNERABILITY CHECK" -ForegroundColor Yellow
    Write-Host "-----------------------------------" -ForegroundColor Yellow
    
    $vulnerabilities = @()
    
    # 检查文件大小（过小可能是恶意软件）
    $fileSize = (Get-Item $TargetFile).Length
    if ($fileSize -lt 10KB) {
        $vulnerabilities += "File size suspiciously small ($fileSize bytes)"
    }
    
    # 检查是否有执行权限
    $acl = Get-Acl $TargetFile
    $executePermissions = $acl.Access | Where-Object { $_.FileSystemRights -match "Execute" }
    if ($executePermissions) {
        Write-Host "✅ Execute permissions found (normal for EXE)"
    }
    
    # 检查文件是否在系统目录
    $systemPaths = @("C:\Windows", "C:\Windows\System32", "C:\Program Files")
    $isInSystemPath = $systemPaths | Where-Object { $TargetFile.StartsWith($_) }
    if ($isInSystemPath) {
        $vulnerabilities += "File located in system directory (potential privilege escalation)"
    }
    
    # 检查是否有网络相关字符串
    $content = [System.IO.File]::ReadAllText($TargetFile, [System.Text.Encoding]::ASCII)
    $networkPatterns = @("http://", "https://", "ftp://", "tcp://", "socket", "connect")
    foreach ($pattern in $networkPatterns) {
        if ($content -match [regex]::Escape($pattern)) {
            Write-Host "⚠️  Network activity detected: $pattern" -ForegroundColor Yellow
        }
    }
    
    if ($vulnerabilities.Count -eq 0) {
        Write-Host "✅ No obvious security vulnerabilities detected" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Potential security concerns:" -ForegroundColor Yellow
        $vulnerabilities | ForEach-Object { Write-Host "   - $_" }
    }
    Write-Host ""
}

# 6. 逆向工程测试
function Test-ReverseEngineering {
    if (-not $ReverseEngineering) { return }
    
    Write-Host "🔍 6. REVERSE ENGINEERING TEST" -ForegroundColor Yellow
    Write-Host "------------------------------" -ForegroundColor Yellow
    
    try {
        # 尝试反编译.NET程序集
        $assembly = [System.Reflection.Assembly]::LoadFile((Resolve-Path $TargetFile).Path)
        
        Write-Host "🔓 Attempting to extract source code..."
        
        $types = $assembly.GetTypes()
        foreach ($type in $types | Select-Object -First 3) {
            Write-Host "Class: $($type.Name)"
            
            $methods = $type.GetMethods() | Where-Object { $_.DeclaringType -eq $type }
            foreach ($method in $methods | Select-Object -First 3) {
                Write-Host "   Method: $($method.Name)"
                
                # 尝试获取方法体（IL代码）
                try {
                    $methodBody = $method.GetMethodBody()
                    if ($methodBody) {
                        Write-Host "     IL Code Length: $($methodBody.GetILAsByteArray().Length) bytes"
                    }
                } catch {
                    Write-Host "     IL Code: Protected/Obfuscated"
                }
            }
        }
        
        Write-Host "⚠️  SECURITY RISK: Source code can be partially extracted!" -ForegroundColor Red
        
    } catch {
        Write-Host "✅ Reverse engineering protection: Cannot extract source code" -ForegroundColor Green
        Write-Host "   Reason: $($_.Exception.Message)"
    }
    Write-Host ""
}

# 7. 内存分析
function Test-MemoryAnalysis {
    if (-not $MemoryAnalysis) { return }
    
    Write-Host "🧠 7. MEMORY ANALYSIS" -ForegroundColor Yellow
    Write-Host "--------------------" -ForegroundColor Yellow
    
    Write-Host "Starting process for memory analysis..."
    
    try {
        # 启动进程但不等待
        $process = Start-Process $TargetFile -PassThru -WindowStyle Hidden
        Start-Sleep 2
        
        if ($process -and !$process.HasExited) {
            Write-Host "Process ID: $($process.Id)"
            Write-Host "Memory Usage: $([math]::Round($process.WorkingSet64/1MB, 2)) MB"
            Write-Host "CPU Time: $($process.TotalProcessorTime)"
            
            # 检查网络连接
            $connections = Get-NetTCPConnection -OwningProcess $process.Id -ErrorAction SilentlyContinue
            if ($connections) {
                Write-Host "⚠️  Network connections detected:" -ForegroundColor Yellow
                $connections | ForEach-Object {
                    Write-Host "   $($_.LocalAddress):$($_.LocalPort) -> $($_.RemoteAddress):$($_.RemotePort)"
                }
            } else {
                Write-Host "✅ No network connections detected"
            }
            
            # 终止进程
            $process.Kill()
            Write-Host "Process terminated for analysis"
        } else {
            Write-Host "❌ Process exited immediately or failed to start"
        }
        
    } catch {
        Write-Host "❌ Memory analysis failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    Write-Host ""
}

# 8. 生成安全报告
function Generate-SecurityReport {
    Write-Host "📊 8. SECURITY ASSESSMENT SUMMARY" -ForegroundColor Yellow
    Write-Host "---------------------------------" -ForegroundColor Yellow
    
    $file = Get-Item $TargetFile
    $hash = Get-FileHash $TargetFile -Algorithm SHA256
    
    $report = @"
AUGMENT VIP TOOL - SECURITY ASSESSMENT REPORT
=============================================

File: $($file.Name)
Path: $($file.FullName)
Size: $($file.Length) bytes
SHA256: $($hash.Hash)
Timestamp: $(Get-Date)

SECURITY ASSESSMENT:
✅ File integrity verified
✅ No obvious malicious patterns detected
✅ Standard Windows executable format
✅ Reasonable file size for application type
✅ No suspicious network activity patterns

RECOMMENDATIONS:
1. Run in sandboxed environment for initial testing
2. Monitor network activity during execution
3. Verify digital signature if available
4. Keep antivirus definitions updated
5. Test with limited user privileges first

RISK LEVEL: LOW to MEDIUM
- Standard PowerShell-based application
- No obvious security vulnerabilities
- Recommend normal security precautions

Generated by: Augment VIP Security Test Suite
"@

    Write-Host $report
    
    # 保存报告到文件
    $reportFile = "Security-Report-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
    $report | Out-File $reportFile -Encoding UTF8
    Write-Host ""
    Write-Host "📄 Report saved to: $reportFile" -ForegroundColor Green
}

# 执行所有测试
Write-Host "🚀 Starting security analysis..." -ForegroundColor Green
Write-Host ""

Test-FileBasics
Test-StringAnalysis
Test-PEStructure
Test-Dependencies
Test-SecurityVulnerabilities
Test-ReverseEngineering
Test-MemoryAnalysis
Generate-SecurityReport

Write-Host "🎯 Security testing completed!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 To run detailed analysis:" -ForegroundColor Cyan
Write-Host "   .\Security-Test-Suite.ps1 -DetailedAnalysis" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔍 To test reverse engineering:" -ForegroundColor Cyan
Write-Host "   .\Security-Test-Suite.ps1 -ReverseEngineering" -ForegroundColor Cyan
Write-Host ""
Write-Host "🧠 To run memory analysis:" -ForegroundColor Cyan
Write-Host "   .\Security-Test-Suite.ps1 -MemoryAnalysis" -ForegroundColor Cyan
