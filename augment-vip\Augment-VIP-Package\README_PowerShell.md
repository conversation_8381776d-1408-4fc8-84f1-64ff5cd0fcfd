# Augment VIP - PowerShell版本

这是Augment VIP项目的PowerShell版本，专为Windows用户设计，无需安装WSL、Git Bash或其他Linux环境。

## 🚀 功能特性

- **数据库清理**: 从VS Code数据库中移除Augment相关条目
- **遥测ID修改**: 为VS Code生成随机遥测ID以增强隐私
- **Windows原生支持**: 使用PowerShell脚本，无需额外环境
- **自动依赖管理**: 自动下载SQLite3（如果需要）
- **安全操作**: 在进行任何更改前创建备份
- **用户友好**: 清晰的彩色输出和详细的状态消息

## 📋 系统要求

- Windows 10/11
- PowerShell 5.1 或更高版本
- VS Code 已安装

## 💻 安装和使用

### 快速开始

1. **克隆项目**（如果还没有）:
   ```powershell
   git clone https://github.com/azrilaiman2003/augment-vip.git
   cd augment-vip
   ```

2. **运行PowerShell安装脚本**:
   ```powershell
   .\install.ps1
   ```

### 安装选项

```powershell
# 基本安装
.\install.ps1

# 安装并清理数据库
.\install.ps1 -Clean

# 安装并修改遥测ID
.\install.ps1 -ModifyIds

# 安装并运行所有脚本
.\install.ps1 -All

# 显示帮助信息
.\install.ps1 -Help
```

### 单独运行脚本

安装完成后，您可以单独运行各个脚本：

```powershell
# 清理VS Code数据库
.\scripts\clean_code_db.ps1

# 修改VS Code遥测ID
.\scripts\id_modifier.ps1

# 显示脚本帮助
.\scripts\clean_code_db.ps1 -Help
.\scripts\id_modifier.ps1 -Help
```

## 🔧 脚本功能详解

### 数据库清理脚本 (clean_code_db.ps1)

- 自动检测VS Code和VS Code Insiders的数据库位置
- 自动下载SQLite3（如果系统中没有）
- 创建数据库备份
- 移除包含"augment"的条目
- 提供详细的操作报告

### 遥测ID修改脚本 (id_modifier.ps1)

- 自动定位VS Code的storage.json文件
- 生成随机的64字符机器ID
- 生成随机的UUID设备ID
- 创建配置文件备份
- 更新遥测标识符

## 🛠️ 故障排除

### 执行策略问题

如果遇到执行策略错误：

```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 权限问题

如果遇到权限问题，请以管理员身份运行PowerShell。

### VS Code未找到

确保VS Code已安装并至少运行过一次，以创建必要的配置文件。

## 📁 项目结构

```
augment-vip/
├── config/                 # 配置文件
├── data/                   # 数据存储
├── logs/                   # 日志文件
├── scripts/                # 脚本目录
│   ├── clean_code_db.ps1   # PowerShell数据库清理脚本
│   ├── id_modifier.ps1     # PowerShell ID修改脚本
│   ├── clean_code_db.sh    # 原始Bash脚本
│   ├── id_modifier.sh      # 原始Bash脚本
│   └── install.sh          # 原始Bash安装脚本
├── temp/                   # 临时文件
├── install.ps1             # PowerShell安装脚本
├── install.sh              # 原始Bash安装脚本
├── README.md               # 原始README
└── README_PowerShell.md    # 本文件
```

## 🔍 工作原理

### 数据库清理

1. **查找数据库**: 自动检测VS Code数据库文件位置
2. **创建备份**: 在修改前创建安全备份
3. **清理条目**: 使用SQLite删除包含"augment"的记录
4. **报告结果**: 提供详细的操作反馈

### 遥测ID修改

1. **定位配置**: 找到VS Code的storage.json文件
2. **生成ID**: 创建随机的机器ID和设备ID
3. **更新配置**: 安全地修改JSON配置文件
4. **验证更改**: 确认修改成功完成

## ⚠️ 重要说明

- 在运行脚本前，建议关闭VS Code
- 所有操作都会创建备份文件
- 修改后可能需要重启VS Code才能生效
- 这些脚本仅用于教育和研究目的

## 🤝 贡献

欢迎提交问题报告和功能请求！

## 📜 许可证

本项目采用MIT许可证 - 详见LICENSE文件。

## 📞 联系方式

原项目作者: Azril Aiman - <EMAIL>
项目链接: https://github.com/azrilaiman2003/augment-vip

---

**注意**: 这是原始Bash脚本的PowerShell移植版本，提供相同的功能但专为Windows环境优化。
