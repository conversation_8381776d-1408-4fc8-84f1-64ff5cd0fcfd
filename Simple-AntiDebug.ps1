# Simple Anti-Debug Protection Script
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    [string]$OutputFile = ""
)

Write-Host "Adding anti-debug protection..." -ForegroundColor Cyan

if (-not (Test-Path $InputFile)) {
    Write-Host "Input file not found: $InputFile" -ForegroundColor Red
    exit 1
}

if ($OutputFile -eq "") {
    $OutputFile = $InputFile.Replace(".ps1", "-AntiDebug.ps1")
}

# Read original code
$originalCode = Get-Content $InputFile -Raw

# Create anti-debug protected code
$protectedCode = @"
# Augment VIP Tool - Anti-Debug Protected Version
# This file is protected against debugging and reverse engineering

# Anti-Debug Protection Functions
function Test-DebuggerAttached {
    return [System.Diagnostics.Debugger]::IsAttached
}

function Test-VirtualMachine {
    try {
        `$bios = Get-WmiObject -Class Win32_BIOS -ErrorAction SilentlyContinue
        if (`$bios -and (`$bios.Manufacturer -match "VMware|VirtualBox|Microsoft Corporation|Xen|QEMU")) {
            return `$true
        }
        return `$false
    } catch {
        return `$false
    }
}

function Test-AnalysisTools {
    try {
        `$suspiciousProcesses = @("ollydbg", "x64dbg", "windbg", "ida", "processhacker", "procmon", "procexp")
        `$runningProcesses = Get-Process | ForEach-Object { `$_.ProcessName.ToLower() }
        
        foreach (`$tool in `$suspiciousProcesses) {
            if (`$runningProcesses -contains `$tool) {
                return `$true
            }
        }
        return `$false
    } catch {
        return `$false
    }
}

function Test-SandboxEnvironment {
    try {
        # Check username
        `$suspiciousUsers = @("sandbox", "malware", "virus", "sample", "test", "analyst")
        `$currentUser = `$env:USERNAME.ToLower()
        if (`$suspiciousUsers -contains `$currentUser) {
            return `$true
        }
        
        # Check computer name
        `$suspiciousNames = @("sandbox", "malware", "virus", "sample", "test", "analyst", "cuckoo")
        `$computerName = `$env:COMPUTERNAME.ToLower()
        if (`$suspiciousNames -contains `$computerName) {
            return `$true
        }
        
        # Check system uptime (sandbox usually has short uptime)
        `$uptime = (Get-Date) - (Get-CimInstance -ClassName Win32_OperatingSystem -ErrorAction SilentlyContinue).LastBootUpTime
        if (`$uptime -and `$uptime.TotalMinutes -lt 10) {
            return `$true
        }
        
        return `$false
    } catch {
        return `$false
    }
}

function Invoke-AntiDebugCheck {
    `$threats = @()
    
    if (Test-DebuggerAttached) {
        `$threats += "Debugger attached"
    }
    
    if (Test-VirtualMachine) {
        `$threats += "Virtual machine detected"
    }
    
    if (Test-AnalysisTools) {
        `$threats += "Analysis tools detected"
    }
    
    if (Test-SandboxEnvironment) {
        `$threats += "Sandbox environment detected"
    }
    
    if (`$threats.Count -gt 0) {
        Write-Host "[SECURITY] THREAT DETECTED!" -ForegroundColor Red
        foreach (`$threat in `$threats) {
            Write-Host "[SECURITY] - `$threat" -ForegroundColor Red
        }
        Write-Host "[SECURITY] Execution terminated for security reasons." -ForegroundColor Red
        Start-Sleep 2
        exit 1
    }
    
    Write-Host "[SECURITY] Anti-debug check passed" -ForegroundColor Green
}

# Perform initial anti-debug check
Invoke-AntiDebugCheck

# Start continuous monitoring
`$monitorTimer = New-Object System.Timers.Timer
`$monitorTimer.Interval = 5000  # 5 seconds
`$monitorTimer.Add_Elapsed({
    Invoke-AntiDebugCheck
})
`$monitorTimer.Start()

Write-Host "[SECURITY] Anti-debug protection active" -ForegroundColor Green

# Execute original code
try {
$originalCode
} finally {
    # Clean up timer
    if (`$monitorTimer) {
        `$monitorTimer.Stop()
        `$monitorTimer.Dispose()
    }
}
"@

# Save protected code
$protectedCode | Out-File $OutputFile -Encoding UTF8

Write-Host "Anti-debug protection added!" -ForegroundColor Green
Write-Host "Input file: $InputFile" -ForegroundColor White
Write-Host "Output file: $OutputFile" -ForegroundColor White
