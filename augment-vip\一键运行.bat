@echo off
chcp 65001 >nul
title Augment VIP - 一键运行工具

:: 设置颜色
color 0A

echo.
echo ========================================
echo     Augment VIP - 一键运行工具
echo ========================================
echo.
echo 正在检查环境...

:: 检查PowerShell
powershell -Command "Write-Host '✓ PowerShell 可用' -ForegroundColor Green"
if errorlevel 1 (
    echo ✗ PowerShell 不可用，请安装 PowerShell
    pause
    exit /b 1
)

:: 检查当前目录
if not exist "scripts\clean_code_db.ps1" (
    echo ✗ 脚本文件未找到，请确保在正确的目录中运行
    pause
    exit /b 1
)

echo ✓ 环境检查完成
echo.

:MENU
echo ========================================
echo           请选择操作
echo ========================================
echo.
echo [1] 完整安装并运行所有功能 (推荐)
echo [2] 仅清理 VS Code 数据库
echo [3] 仅修改遥测 ID
echo [4] 查看帮助信息
echo [5] 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto FULL_INSTALL
if "%choice%"=="2" goto CLEAN_DB
if "%choice%"=="3" goto MODIFY_ID
if "%choice%"=="4" goto SHOW_HELP
if "%choice%"=="5" goto EXIT
echo 无效选择，请重新输入
goto MENU

:FULL_INSTALL
echo.
echo ========================================
echo       执行完整安装和清理
echo ========================================
echo.
echo 正在运行完整安装...
powershell -ExecutionPolicy Bypass -File ".\install.ps1" -All
echo.
echo 操作完成！
pause
goto MENU

:CLEAN_DB
echo.
echo ========================================
echo        清理 VS Code 数据库
echo ========================================
echo.
echo 正在清理数据库...
powershell -ExecutionPolicy Bypass -File ".\scripts\clean_code_db.ps1"
echo.
echo 操作完成！
pause
goto MENU

:MODIFY_ID
echo.
echo ========================================
echo         修改遥测 ID
echo ========================================
echo.
echo 正在修改遥测 ID...
powershell -ExecutionPolicy Bypass -File ".\scripts\id_modifier.ps1"
echo.
echo 操作完成！
pause
goto MENU

:SHOW_HELP
echo.
echo ========================================
echo           帮助信息
echo ========================================
echo.
echo Augment VIP 工具说明：
echo.
echo 1. 数据库清理功能：
echo    - 移除 VS Code 数据库中的 Augment 相关条目
echo    - 自动创建备份文件
echo    - 支持 VS Code 和 VS Code Insiders
echo.
echo 2. 遥测 ID 修改功能：
echo    - 生成随机的机器 ID 和设备 ID
echo    - 修改 VS Code 的遥测标识符
echo    - 自动创建配置文件备份
echo.
echo 重要提示：
echo - 使用前请关闭 VS Code
echo - 修改后需要重启 VS Code 才能生效
echo - 所有操作都会自动创建备份文件
echo - 本工具仅用于教育和研究目的
echo.
pause
goto MENU

:EXIT
echo.
echo 感谢使用 Augment VIP 工具！
echo.
pause
exit /b 0
