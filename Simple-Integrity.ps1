# Simple Integrity Protection Script
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    [string]$OutputFile = ""
)

Write-Host "Adding integrity protection..." -ForegroundColor Cyan

if (-not (Test-Path $InputFile)) {
    Write-Host "Input file not found: $InputFile" -ForegroundColor Red
    exit 1
}

if ($OutputFile -eq "") {
    $OutputFile = $InputFile.Replace(".ps1", "-Protected.ps1")
}

# Read original code
$originalCode = Get-Content $InputFile -Raw

# Calculate hash
$codeBytes = [System.Text.Encoding]::UTF8.GetBytes($originalCode)
$sha256 = [System.Security.Cryptography.SHA256]::Create()
$hashBytes = $sha256.ComputeHash($codeBytes)
$codeHash = [System.BitConverter]::ToString($hashBytes).Replace("-", "").ToLower()
$sha256.Dispose()

# Generate HMAC
$secretKey = "AugmentVIP-Integrity-2025"
$keyBytes = [System.Text.Encoding]::UTF8.GetBytes($secretKey)
$hmac = New-Object System.Security.Cryptography.HMACSHA256
$hmac.Key = $keyBytes
$signatureBytes = $hmac.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($codeHash))
$signature = [System.Convert]::ToBase64String($signatureBytes)
$hmac.Dispose()

# Create protected code
$protectedCode = @"
# Augment VIP Tool - Integrity Protected Version
# DO NOT MODIFY - This file is protected against tampering

# Integrity verification
`$expectedHash = "$codeHash"
`$expectedSignature = "$signature"
`$secretKey = "$secretKey"

function Verify-Integrity {
    param([string]`$Code)
    
    try {
        # Calculate current hash
        `$codeBytes = [System.Text.Encoding]::UTF8.GetBytes(`$Code)
        `$sha256 = [System.Security.Cryptography.SHA256]::Create()
        `$hashBytes = `$sha256.ComputeHash(`$codeBytes)
        `$currentHash = [System.BitConverter]::ToString(`$hashBytes).Replace("-", "").ToLower()
        `$sha256.Dispose()
        
        # Verify hash
        if (`$currentHash -ne `$expectedHash) {
            return `$false
        }
        
        # Verify HMAC
        `$keyBytes = [System.Text.Encoding]::UTF8.GetBytes(`$secretKey)
        `$hmac = New-Object System.Security.Cryptography.HMACSHA256
        `$hmac.Key = `$keyBytes
        `$signatureBytes = `$hmac.ComputeHash([System.Text.Encoding]::UTF8.GetBytes(`$currentHash))
        `$currentSignature = [System.Convert]::ToBase64String(`$signatureBytes)
        `$hmac.Dispose()
        
        return (`$currentSignature -eq `$expectedSignature)
    } catch {
        return `$false
    }
}

# Original code
`$originalCode = @'
$originalCode
'@

# Verify integrity before execution
if (-not (Verify-Integrity -Code `$originalCode)) {
    Write-Host "[SECURITY] CRITICAL: Code integrity verification FAILED!" -ForegroundColor Red
    Write-Host "[SECURITY] The application code has been tampered with." -ForegroundColor Red
    Write-Host "[SECURITY] Execution terminated for security reasons." -ForegroundColor Red
    exit 1
}

Write-Host "[SECURITY] Code integrity verified successfully" -ForegroundColor Green

# Execute original code
Invoke-Expression `$originalCode
"@

# Save protected code
$protectedCode | Out-File $OutputFile -Encoding UTF8

Write-Host "Integrity protection added!" -ForegroundColor Green
Write-Host "Input file: $InputFile" -ForegroundColor White
Write-Host "Output file: $OutputFile" -ForegroundColor White
Write-Host "Hash: $($codeHash.Substring(0,16))..." -ForegroundColor Cyan
