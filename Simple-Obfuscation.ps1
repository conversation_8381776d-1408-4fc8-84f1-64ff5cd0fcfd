# Simple PowerShell Obfuscation Script
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    [string]$OutputFile = ""
)

Write-Host "Starting PowerShell obfuscation..." -ForegroundColor Cyan

if (-not (Test-Path $InputFile)) {
    Write-Host "Input file not found: $InputFile" -ForegroundColor Red
    exit 1
}

if ($OutputFile -eq "") {
    $OutputFile = $InputFile.Replace(".ps1", "-Obfuscated.ps1")
}

# Read original code
$originalCode = Get-Content $InputFile -Raw

# Simple variable obfuscation
function Obfuscate-Variables {
    param([string]$Code)
    
    Write-Host "Obfuscating variables..." -ForegroundColor Yellow
    
    # Simple variable name replacements
    $variableMap = @{
        'form' = 'obj1'
        'button' = 'obj2'
        'label' = 'obj3'
        'textBox' = 'obj4'
        'panel' = 'obj5'
        'result' = 'var1'
        'output' = 'var2'
        'input' = 'var3'
        'data' = 'var4'
        'file' = 'var5'
    }
    
    foreach ($oldVar in $variableMap.Keys) {
        $newVar = $variableMap[$oldVar]
        $Code = $Code -replace "\`$$oldVar\b", "`$$newVar"
    }
    
    Write-Host "Variables obfuscated" -ForegroundColor Green
    return $Code
}

# Simple string obfuscation
function Obfuscate-Strings {
    param([string]$Code)
    
    Write-Host "Obfuscating strings..." -ForegroundColor Yellow
    
    # Encode some common strings
    $stringMap = @{
        '"Augment VIP Tool"' = '[System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String("QXVnbWVudCBWSVAgVG9vbA=="))'
        '"VS Code"' = '[System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String("VlMgQ29kZQ=="))'
        '"Success"' = '[System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String("U3VjY2Vzcw=="))'
        '"Error"' = '[System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String("RXJyb3I="))'
    }
    
    foreach ($oldString in $stringMap.Keys) {
        $newString = $stringMap[$oldString]
        $Code = $Code -replace [regex]::Escape($oldString), $newString
    }
    
    Write-Host "Strings obfuscated" -ForegroundColor Green
    return $Code
}

# Add junk code
function Add-JunkCode {
    param([string]$Code)
    
    Write-Host "Adding junk code..." -ForegroundColor Yellow
    
    $junkCode = @"
# Obfuscated code - Anti-reverse engineering
`$null = @(
    [System.GC]::Collect()
    [System.Threading.Thread]::Sleep(1)
    Get-Date | Out-Null
    `$env:COMPUTERNAME | Out-Null
    [System.Environment]::TickCount | Out-Null
)

# Random variables for obfuscation
`$rnd1 = Get-Random
`$rnd2 = [System.Environment]::TickCount
`$rnd3 = [System.Guid]::NewGuid().ToString()

"@
    
    $Code = $junkCode + "`n" + $Code
    
    Write-Host "Junk code added" -ForegroundColor Green
    return $Code
}

# Apply obfuscation
Write-Host "Starting obfuscation process..." -ForegroundColor Green
Write-Host ""

$obfuscatedCode = $originalCode
$obfuscatedCode = Obfuscate-Variables -Code $obfuscatedCode
$obfuscatedCode = Obfuscate-Strings -Code $obfuscatedCode
$obfuscatedCode = Add-JunkCode -Code $obfuscatedCode

# Save obfuscated code
$obfuscatedCode | Out-File $OutputFile -Encoding UTF8

Write-Host ""
Write-Host "Obfuscation completed!" -ForegroundColor Green
Write-Host "Input file: $InputFile" -ForegroundColor White
Write-Host "Output file: $OutputFile" -ForegroundColor White
Write-Host "Original size: $($originalCode.Length) characters" -ForegroundColor White
Write-Host "Obfuscated size: $($obfuscatedCode.Length) characters" -ForegroundColor White
