# id_modifier.ps1
#
# Description: Modifies telemetry IDs in VS Code storage.json file
# This script generates random values for machineId and devDeviceId

param(
    [switch]$Help
)

# Show help message
if ($Help) {
    Write-Host "Augment VIP ID Modifier (PowerShell Version)" -ForegroundColor Green
    Write-Host ""
    Write-Host "Description: Modifies VS Code telemetry IDs for enhanced privacy"
    Write-Host ""
    Write-Host "Usage: .\id_modifier.ps1"
    Write-Host ""
    Write-Host "Requirements:"
    Write-Host "  - VS Code installed"
    Write-Host ""
    exit 0
}

# Function to write colored output
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "INFO" { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Blue }
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green }
        "WARNING" { Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor Yellow }
        "ERROR" { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red }
    }
}

# Function to get VS Code storage.json path
function Get-VSCodeStoragePath {
    $appDataPath = $env:APPDATA
    if (-not $appDataPath) {
        Write-Log "APPDATA environment variable not found" "ERROR"
        return $null
    }
    
    $storagePath = Join-Path $appDataPath "Code\User\globalStorage\storage.json"
    
    if (-not (Test-Path $storagePath)) {
        # Also check for VS Code Insiders
        $storagePathInsiders = Join-Path $appDataPath "Code - Insiders\User\globalStorage\storage.json"
        if (Test-Path $storagePathInsiders) {
            return $storagePathInsiders
        }
        
        Write-Log "Storage file not found at: $storagePath" "ERROR"
        return $null
    }
    
    return $storagePath
}

# Function to generate a random 64-character hex string for machineId
function New-MachineId {
    $bytes = New-Object byte[] 32
    $rng = [System.Security.Cryptography.RNGCryptoServiceProvider]::Create()
    $rng.GetBytes($bytes)
    $rng.Dispose()
    
    return [System.BitConverter]::ToString($bytes).Replace("-", "").ToLower()
}

# Function to generate a random UUID v4 for devDeviceId
function New-DeviceId {
    return [System.Guid]::NewGuid().ToString().ToLower()
}

# Function to modify the storage.json file
function Update-StorageFile {
    param(
        [string]$StoragePath,
        [string]$MachineId,
        [string]$DeviceId
    )
    
    Write-Log "Modifying storage file at: $StoragePath" "INFO"
    
    # Create backup
    $backupPath = "$StoragePath.backup"
    try {
        Copy-Item $StoragePath $backupPath -Force
        Write-Log "Created backup at: $backupPath" "SUCCESS"
    }
    catch {
        Write-Log "Failed to create backup: $($_.Exception.Message)" "ERROR"
        return $false
    }
    
    try {
        # Read the current file
        $content = Get-Content $StoragePath -Raw -Encoding UTF8
        
        # Parse JSON
        $jsonObject = $content | ConvertFrom-Json
        
        # Update the values
        $jsonObject | Add-Member -Type NoteProperty -Name "telemetry.machineId" -Value $MachineId -Force
        $jsonObject | Add-Member -Type NoteProperty -Name "telemetry.devDeviceId" -Value $DeviceId -Force
        
        # Convert back to JSON and save
        $updatedContent = $jsonObject | ConvertTo-Json -Depth 100
        $updatedContent | Set-Content $StoragePath -Encoding UTF8
        
        Write-Log "Successfully updated telemetry IDs" "SUCCESS"
        Write-Log "New machineId: $MachineId" "INFO"
        Write-Log "New devDeviceId: $DeviceId" "INFO"
        
        return $true
    }
    catch {
        Write-Log "Failed to modify storage file: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main execution
function Main {
    Write-Log "Starting VS Code telemetry ID modification" "INFO"
    
    # Get storage.json path
    $storagePath = Get-VSCodeStoragePath
    if (-not $storagePath) {
        Write-Log "Cannot find VS Code storage file. Make sure VS Code is installed and has been run at least once." "ERROR"
        exit 1
    }
    
    Write-Log "Found storage file at: $storagePath" "SUCCESS"
    
    # Generate new IDs
    Write-Log "Generating new telemetry IDs..." "INFO"
    $machineId = New-MachineId
    $deviceId = New-DeviceId
    
    # Modify the file
    if (Update-StorageFile $storagePath $machineId $deviceId) {
        Write-Log "VS Code telemetry IDs have been successfully modified" "SUCCESS"
        Write-Log "You may need to restart VS Code for changes to take effect" "INFO"
    }
    else {
        Write-Log "Failed to modify VS Code telemetry IDs" "ERROR"
        exit 1
    }
}

# Execute main function
Main
