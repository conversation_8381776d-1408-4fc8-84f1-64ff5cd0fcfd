# install.ps1
#
# Description: PowerShell installation script for the Augment VIP project
# This script sets up the necessary configurations for Windows users
#
# Usage: .\install.ps1 [options]
#   Options:
#     -Help           Show this help message
#     -Clean          Run database cleaning script after installation
#     -ModifyIds      Run telemetry ID modification script after installation
#     -All            Run all scripts (clean and modify IDs)

param(
    [switch]$Help,
    [switch]$Clean,
    [switch]$ModifyIds,
    [switch]$All
)

# Show help message
if ($Help) {
    Write-Host "Augment VIP Installation Script (PowerShell Version)" -ForegroundColor Green
    Write-Host ""
    Write-Host "Description: Sets up Augment VIP tools for Windows users"
    Write-Host ""
    Write-Host "Usage: .\install.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Help           Show this help message"
    Write-Host "  -Clean          Run database cleaning script after installation"
    Write-Host "  -ModifyIds      Run telemetry ID modification script after installation"
    Write-Host "  -All            Run all scripts (clean and modify IDs)"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\install.ps1                    # Basic installation"
    Write-Host "  .\install.ps1 -Clean             # Install and clean databases"
    Write-Host "  .\install.ps1 -All               # Install and run all scripts"
    Write-Host ""
    exit 0
}

# Function to write colored output
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "INFO" { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Blue }
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green }
        "WARNING" { Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor Yellow }
        "ERROR" { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red }
    }
}

# Function to create project directories
function New-ProjectDirectories {
    Write-Log "Creating project directories..." "INFO"
    
    $directories = @("config", "logs", "data", "temp")
    
    foreach ($dir in $directories) {
        $dirPath = Join-Path (Get-Location) $dir
        if (-not (Test-Path $dirPath)) {
            New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
            Write-Log "Created directory: $dir" "SUCCESS"
        }
        else {
            Write-Log "Directory already exists: $dir" "INFO"
        }
    }
}

# Function to create default configuration
function New-Configuration {
    Write-Log "Setting up project configuration..." "INFO"
    
    $configDir = Join-Path (Get-Location) "config"
    $configFile = Join-Path $configDir "config.json"
    
    if (-not (Test-Path $configFile)) {
        $config = @{
            version = "1.0.0"
            environment = "development"
            features = @{
                cleanCodeDb = $true
                modifyIds = $true
            }
            platform = "windows"
            powershell = $true
        }
        
        $config | ConvertTo-Json -Depth 3 | Set-Content $configFile -Encoding UTF8
        Write-Log "Created default configuration file" "SUCCESS"
    }
    else {
        Write-Log "Configuration file already exists, skipping" "INFO"
    }
}

# Function to check PowerShell execution policy
function Test-ExecutionPolicy {
    $policy = Get-ExecutionPolicy
    if ($policy -eq "Restricted") {
        Write-Log "PowerShell execution policy is Restricted" "WARNING"
        Write-Log "You may need to run: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" "INFO"
        
        $response = Read-Host "Would you like to change the execution policy now? (y/n)"
        if ($response -eq "y" -or $response -eq "Y") {
            try {
                Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
                Write-Log "Execution policy updated successfully" "SUCCESS"
            }
            catch {
                Write-Log "Failed to update execution policy: $($_.Exception.Message)" "ERROR"
                Write-Log "You may need to run PowerShell as Administrator" "INFO"
            }
        }
    }
    else {
        Write-Log "PowerShell execution policy is acceptable: $policy" "SUCCESS"
    }
}

# Function to run the database cleaning script
function Invoke-CleanScript {
    Write-Log "Running database cleaning script..." "INFO"
    
    $scriptPath = Join-Path (Get-Location) "scripts\clean_code_db.ps1"
    if (Test-Path $scriptPath) {
        try {
            & $scriptPath
            Write-Log "Database cleaning completed" "SUCCESS"
            return $true
        }
        catch {
            Write-Log "Database cleaning failed: $($_.Exception.Message)" "ERROR"
            return $false
        }
    }
    else {
        Write-Log "Database cleaning script not found at: $scriptPath" "ERROR"
        return $false
    }
}

# Function to run the telemetry ID modification script
function Invoke-IdModifierScript {
    Write-Log "Running telemetry ID modification script..." "INFO"
    
    $scriptPath = Join-Path (Get-Location) "scripts\id_modifier.ps1"
    if (Test-Path $scriptPath) {
        try {
            & $scriptPath
            Write-Log "Telemetry ID modification completed" "SUCCESS"
            return $true
        }
        catch {
            Write-Log "Telemetry ID modification failed: $($_.Exception.Message)" "ERROR"
            return $false
        }
    }
    else {
        Write-Log "Telemetry ID modification script not found at: $scriptPath" "ERROR"
        return $false
    }
}

# Main execution
function Main {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    Augment VIP Installation Script    " -ForegroundColor Cyan
    Write-Host "         PowerShell Version            " -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    # Handle command line arguments
    if ($All) {
        $Clean = $true
        $ModifyIds = $true
        Write-Log "Running installation with all options enabled" "INFO"
    }
    elseif (-not $Clean -and -not $ModifyIds) {
        Write-Log "Starting basic installation process for Augment VIP" "INFO"
    }
    else {
        Write-Log "Starting installation process with selected options" "INFO"
    }
    
    # Check execution policy
    Test-ExecutionPolicy
    
    # Create directories
    New-ProjectDirectories
    
    # Setup configuration
    New-Configuration
    
    Write-Log "Installation completed successfully!" "SUCCESS"
    
    # Run additional scripts based on parameters
    if ($Clean -or $ModifyIds) {
        Write-Host ""
        Write-Log "Running additional scripts..." "INFO"
        
        if ($Clean) {
            Invoke-CleanScript
        }
        
        if ($ModifyIds) {
            Invoke-IdModifierScript
        }
    }
    else {
        # Ask user if they want to run scripts
        Write-Host ""
        $response = Read-Host "Would you like to clean VS Code databases now? (y/n)"
        if ($response -eq "y" -or $response -eq "Y") {
            Invoke-CleanScript
        }
        
        $response = Read-Host "Would you like to modify VS Code telemetry IDs now? (y/n)"
        if ($response -eq "y" -or $response -eq "Y") {
            Invoke-IdModifierScript
        }
    }
    
    Write-Host ""
    Write-Log "Setup complete! You can now use the PowerShell scripts:" "INFO"
    Write-Log "  - To clean VS Code databases: .\scripts\clean_code_db.ps1" "INFO"
    Write-Log "  - To modify telemetry IDs: .\scripts\id_modifier.ps1" "INFO"
    Write-Host ""
    Write-Host "Note: These PowerShell scripts provide the same functionality as the original Bash scripts" -ForegroundColor Yellow
    Write-Host "but are designed to work natively on Windows without requiring WSL or Git Bash." -ForegroundColor Yellow
}

# Execute main function
Main
