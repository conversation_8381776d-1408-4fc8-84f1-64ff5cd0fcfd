# Simple License Protection with Verification Code
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    [string]$OutputFile = "",
    [string]$LicenseKey = "AKDJFDHSKOMGRIOINOFWEOIPEWFIOM3289589894393290543"
)

Write-Host "Adding license protection..." -ForegroundColor Cyan

if (-not (Test-Path $InputFile)) {
    Write-Host "Input file not found: $InputFile" -ForegroundColor Red
    exit 1
}

if ($OutputFile -eq "") {
    $OutputFile = $InputFile.Replace(".ps1", "-Licensed.ps1")
}

# Read original code
$originalCode = Get-Content $InputFile -Raw

# Generate license hash
$licenseHash = [System.Security.Cryptography.SHA256]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($LicenseKey))
$licenseHashString = [System.BitConverter]::ToString($licenseHash).Replace("-", "").ToLower()

Write-Host "License hash generated: $($licenseHashString.Substring(0,16))..." -ForegroundColor Green

# Create license protection code
$licenseProtectionCode = @"
# AUGMENT VIP TOOL - LICENSED VERSION
# This version requires a valid license key to run

Write-Host ""
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "           AUGMENT VIP TOOL - ENTERPRISE EDITION               " -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "Product: Augment VIP Tool Enterprise Security Edition" -ForegroundColor White
Write-Host "Version: v2.0 Licensed" -ForegroundColor White
Write-Host "Protection: License Key Required" -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

# License validation function
function Test-LicenseKey {
    param([string]`$InputKey)
    
    try {
        if (-not `$InputKey -or `$InputKey.Length -eq 0) {
            return `$false
        }
        
        # Calculate hash of input key
        `$keyBytes = [System.Text.Encoding]::UTF8.GetBytes(`$InputKey.Trim())
        `$sha256 = [System.Security.Cryptography.SHA256]::Create()
        `$hashBytes = `$sha256.ComputeHash(`$keyBytes)
        `$inputHash = [System.BitConverter]::ToString(`$hashBytes).Replace("-", "").ToLower()
        `$sha256.Dispose()
        
        # Expected hash
        `$expectedHash = "$licenseHashString"
        
        # Compare hashes
        return (`$inputHash -eq `$expectedHash)
    } catch {
        return `$false
    }
}

# Prompt for license key
Write-Host "Please enter your license key to continue:" -ForegroundColor Yellow
Write-Host "(License key is case-sensitive)" -ForegroundColor Gray
Write-Host ""

`$userInput = Read-Host "License Key"

# Validate license
Write-Host ""
Write-Host "Validating license..." -ForegroundColor Yellow

if (Test-LicenseKey -InputKey `$userInput) {
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Green
    Write-Host "                    LICENSE VALIDATION SUCCESS                 " -ForegroundColor Green
    Write-Host "================================================================" -ForegroundColor Green
    Write-Host "Status: License key is valid" -ForegroundColor White
    Write-Host "Authorization: Granted" -ForegroundColor White
    Write-Host "Validation Time: `$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
    Write-Host "User: `$env:USERNAME@`$env:COMPUTERNAME" -ForegroundColor White
    Write-Host "================================================================" -ForegroundColor Green
    Write-Host ""
    
    # Log successful access
    try {
        `$logEntry = "`$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - AUTHORIZED ACCESS - User: `$env:USERNAME - Computer: `$env:COMPUTERNAME"
        `$logDir = "logs"
        if (-not (Test-Path `$logDir)) {
            New-Item -ItemType Directory -Path `$logDir -Force | Out-Null
        }
        Add-Content -Path "`$logDir\license-access.log" -Value `$logEntry -Encoding UTF8
    } catch {
        # Ignore logging errors
    }
    
    Write-Host "Starting application..." -ForegroundColor Green
    Start-Sleep 2
    
    # Execute original code
$originalCode
    
} else {
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Red
    Write-Host "                    LICENSE VALIDATION FAILED                  " -ForegroundColor Red
    Write-Host "================================================================" -ForegroundColor Red
    Write-Host "Error: Invalid license key" -ForegroundColor White
    Write-Host "Solution: Please contact software provider for valid license" -ForegroundColor White
    Write-Host "Support: <EMAIL>" -ForegroundColor White
    Write-Host "Warning: Unauthorized use of this software is illegal" -ForegroundColor Yellow
    Write-Host "================================================================" -ForegroundColor Red
    Write-Host ""
    
    # Log unauthorized access attempt
    try {
        `$logEntry = "`$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - UNAUTHORIZED ACCESS ATTEMPT - User: `$env:USERNAME - Computer: `$env:COMPUTERNAME - Input: `$(`$userInput.Substring(0, [Math]::Min(10, `$userInput.Length)))..."
        `$logDir = "logs"
        if (-not (Test-Path `$logDir)) {
            New-Item -ItemType Directory -Path `$logDir -Force | Out-Null
        }
        Add-Content -Path "`$logDir\license-violations.log" -Value `$logEntry -Encoding UTF8
    } catch {
        # Ignore logging errors
    }
    
    Write-Host "Application will exit in 3 seconds..." -ForegroundColor Red
    Start-Sleep 3
    exit 1
}
"@

# Save licensed code
$licenseProtectionCode | Out-File $OutputFile -Encoding UTF8

Write-Host ""
Write-Host "License protection added successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Protection Features:" -ForegroundColor Cyan
Write-Host "  - Startup license key validation" -ForegroundColor Green
Write-Host "  - SHA256 hash verification" -ForegroundColor Green
Write-Host "  - Unauthorized access logging" -ForegroundColor Green
Write-Host "  - Authorized access logging" -ForegroundColor Green
Write-Host ""
Write-Host "License Information:" -ForegroundColor Yellow
Write-Host "  License Key: $LicenseKey" -ForegroundColor White
Write-Host "  Hash: $($licenseHashString.Substring(0,32))..." -ForegroundColor Gray
Write-Host ""
Write-Host "Output File: $OutputFile" -ForegroundColor Cyan
Write-Host ""
Write-Host "Usage Instructions:" -ForegroundColor Yellow
Write-Host "  1. Run the program and enter the license key when prompted" -ForegroundColor White
Write-Host "  2. Only the correct license key will allow the program to start" -ForegroundColor White
Write-Host "  3. All access attempts are logged for security auditing" -ForegroundColor White
