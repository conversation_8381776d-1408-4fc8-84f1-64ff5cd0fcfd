@echo off
chcp 65001 >nul
title Augment VIP - 自动安装程序

:: 设置颜色
color 0B

echo.
echo ========================================
echo     Augment VIP - 自动安装程序
echo ========================================
echo.
echo 此程序将自动完成以下操作：
echo 1. 检查系统环境
echo 2. 创建必要的目录结构
echo 3. 下载所需依赖项
echo 4. 配置运行环境
echo 5. 提供一键运行选项
echo.

set /p confirm=是否继续安装？(Y/N): 
if /i not "%confirm%"=="Y" (
    echo 安装已取消
    pause
    exit /b 0
)

echo.
echo ========================================
echo         开始自动安装
echo ========================================
echo.

:: 步骤1: 检查PowerShell
echo [1/5] 检查 PowerShell 环境...
powershell -Command "Write-Host '✓ PowerShell 版本: ' -NoNewline -ForegroundColor Green; $PSVersionTable.PSVersion"
if errorlevel 1 (
    echo ✗ PowerShell 不可用，请安装 PowerShell
    pause
    exit /b 1
)

:: 步骤2: 创建目录结构
echo [2/5] 创建项目目录结构...
if not exist "config" mkdir config
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "temp" mkdir temp
if not exist "scripts" mkdir scripts
echo ✓ 目录结构创建完成

:: 步骤3: 检查脚本文件
echo [3/5] 检查脚本文件...
if exist "scripts\clean_code_db.ps1" (
    echo ✓ 数据库清理脚本已存在
) else (
    echo ✗ 数据库清理脚本未找到
)

if exist "scripts\id_modifier.ps1" (
    echo ✓ ID修改脚本已存在
) else (
    echo ✗ ID修改脚本未找到
)

if exist "install.ps1" (
    echo ✓ 安装脚本已存在
) else (
    echo ✗ 安装脚本未找到
)

:: 步骤4: 运行PowerShell安装脚本
echo [4/5] 运行 PowerShell 安装脚本...
if exist "install.ps1" (
    powershell -ExecutionPolicy Bypass -File ".\install.ps1"
    echo ✓ PowerShell 安装脚本执行完成
) else (
    echo ⚠ PowerShell 安装脚本未找到，跳过此步骤
)

:: 步骤5: 创建桌面快捷方式（可选）
echo [5/5] 创建快捷方式...
set /p shortcut=是否在桌面创建快捷方式？(Y/N): 
if /i "%shortcut%"=="Y" (
    set "desktop=%USERPROFILE%\Desktop"
    set "current_dir=%CD%"
    
    :: 创建批处理快捷方式
    echo @echo off > "%desktop%\Augment VIP.bat"
    echo cd /d "%current_dir%" >> "%desktop%\Augment VIP.bat"
    echo call "一键运行.bat" >> "%desktop%\Augment VIP.bat"
    
    echo ✓ 桌面快捷方式已创建
)

echo.
echo ========================================
echo         安装完成！
echo ========================================
echo.
echo 安装已成功完成！您现在可以：
echo.
echo 1. 双击 "一键运行.bat" 使用图形界面
echo 2. 运行 PowerShell 脚本进行高级操作
echo 3. 使用桌面快捷方式（如果已创建）
echo.
echo 重要提示：
echo - 使用前请关闭 VS Code
echo - 修改后需要重启 VS Code
echo - 所有操作都会自动备份
echo.

set /p run_now=是否现在运行一键工具？(Y/N): 
if /i "%run_now%"=="Y" (
    call "一键运行.bat"
) else (
    echo.
    echo 您可以随时双击 "一键运行.bat" 来使用工具
    pause
)

exit /b 0
