# 一键安全加固脚本
# One-Click Security Hardening Script

param(
    [Parameter(Mandatory=$true)]
    [string]$TargetFile,
    [switch]$AdvancedObfuscation,
    [switch]$SkipSigning,
    [switch]$SkipPacking,
    [string]$OutputDir = "Hardened"
)

Write-Host "🔒 AUGMENT VIP TOOL - 一键安全加固" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

if (-not (Test-Path $TargetFile)) {
    Write-Host "❌ 目标文件不存在: $TargetFile" -ForegroundColor Red
    exit 1
}

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

$fileName = [System.IO.Path]::GetFileNameWithoutExtension($TargetFile)
$fileExt = [System.IO.Path]::GetExtension($TargetFile)

Write-Host "🎯 目标文件: $TargetFile" -ForegroundColor Green
Write-Host "📁 输出目录: $OutputDir" -ForegroundColor Green
Write-Host ""

# 步骤1: 代码混淆
Write-Host "🔤 步骤1: 代码混淆保护" -ForegroundColor Yellow
Write-Host "----------------------" -ForegroundColor Yellow

if ($fileExt -eq ".ps1") {
    $obfuscatedFile = Join-Path $OutputDir "$fileName-Obfuscated.ps1"
    
    try {
        if ($AdvancedObfuscation) {
            & ".\Obfuscate-PowerShell.ps1" -InputFile $TargetFile -OutputFile $obfuscatedFile -Advanced
        } else {
            & ".\Obfuscate-PowerShell.ps1" -InputFile $TargetFile -OutputFile $obfuscatedFile
        }
        
        if (Test-Path $obfuscatedFile) {
            Write-Host "✅ 代码混淆完成: $obfuscatedFile" -ForegroundColor Green
            $currentFile = $obfuscatedFile
        } else {
            Write-Host "⚠️ 代码混淆失败，继续使用原文件" -ForegroundColor Yellow
            $currentFile = $TargetFile
        }
    } catch {
        Write-Host "⚠️ 代码混淆出错: $($_.Exception.Message)" -ForegroundColor Yellow
        $currentFile = $TargetFile
    }
} else {
    Write-Host "ℹ️ 非PowerShell文件，跳过代码混淆" -ForegroundColor Cyan
    $currentFile = $TargetFile
}

Write-Host ""

# 步骤2: 完整性保护
Write-Host "🛡️ 步骤2: 完整性保护" -ForegroundColor Yellow
Write-Host "-------------------" -ForegroundColor Yellow

if ($fileExt -eq ".ps1") {
    $protectedFile = Join-Path $OutputDir "$fileName-Protected.ps1"
    
    try {
        . ".\Integrity-Protection.ps1"
        $result = Add-IntegrityProtection -InputFile $currentFile -OutputFile $protectedFile
        
        if ($result -and (Test-Path $protectedFile)) {
            Write-Host "✅ 完整性保护完成: $protectedFile" -ForegroundColor Green
            $currentFile = $protectedFile
        } else {
            Write-Host "⚠️ 完整性保护失败，继续使用当前文件" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ 完整性保护出错: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "ℹ️ 非PowerShell文件，跳过完整性保护" -ForegroundColor Cyan
}

Write-Host ""

# 步骤3: 反调试保护
Write-Host "🚫 步骤3: 反调试保护" -ForegroundColor Yellow
Write-Host "------------------" -ForegroundColor Yellow

if ($fileExt -eq ".ps1") {
    $antiDebugFile = Join-Path $OutputDir "$fileName-AntiDebug.ps1"
    
    try {
        . ".\Anti-Debug-Protection.ps1"
        $result = Add-AntiDebugProtection -InputFile $currentFile -OutputFile $antiDebugFile
        
        if ($result -and (Test-Path $antiDebugFile)) {
            Write-Host "✅ 反调试保护完成: $antiDebugFile" -ForegroundColor Green
            $currentFile = $antiDebugFile
        } else {
            Write-Host "⚠️ 反调试保护失败，继续使用当前文件" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ 反调试保护出错: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "ℹ️ 非PowerShell文件，跳过反调试保护" -ForegroundColor Cyan
}

Write-Host ""

# 步骤4: 加壳保护
Write-Host "📦 步骤4: 加壳保护" -ForegroundColor Yellow
Write-Host "----------------" -ForegroundColor Yellow

if (-not $SkipPacking) {
    $packedFile = Join-Path $OutputDir "$fileName-Packed$fileExt"
    
    try {
        & ".\Packer-Protection.ps1" -InputFile $currentFile -OutputFile $packedFile
        
        if (Test-Path $packedFile) {
            Write-Host "✅ 加壳保护完成: $packedFile" -ForegroundColor Green
            $currentFile = $packedFile
        } else {
            Write-Host "⚠️ 加壳保护失败，继续使用当前文件" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ 加壳保护出错: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "ℹ️ 跳过加壳保护" -ForegroundColor Cyan
}

Write-Host ""

# 步骤5: 数字签名
Write-Host "✍️ 步骤5: 数字签名" -ForegroundColor Yellow
Write-Host "----------------" -ForegroundColor Yellow

if (-not $SkipSigning) {
    try {
        # 检查是否存在签名证书
        if (Test-Path "AugmentVIP-CodeSigning.pfx") {
            Write-Host "发现现有证书，开始签名..." -ForegroundColor Cyan
            
            & ".\Sign-AugmentVIP.ps1" -FilePath $currentFile
            
            # 验证签名
            $signature = Get-AuthenticodeSignature $currentFile
            if ($signature.Status -eq "Valid") {
                Write-Host "✅ 数字签名完成" -ForegroundColor Green
            } else {
                Write-Host "⚠️ 数字签名状态: $($signature.Status)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "未找到签名证书，创建新证书..." -ForegroundColor Cyan
            & ".\Create-CodeSigning-Certificate.ps1"
        }
    } catch {
        Write-Host "⚠️ 数字签名出错: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "ℹ️ 跳过数字签名" -ForegroundColor Cyan
}

Write-Host ""

# 步骤6: 生成最终的安全版本
Write-Host "🎯 步骤6: 生成最终版本" -ForegroundColor Yellow
Write-Host "--------------------" -ForegroundColor Yellow

$finalFile = Join-Path $OutputDir "$fileName-Hardened$fileExt"
Copy-Item $currentFile $finalFile -Force

Write-Host "✅ 最终安全版本: $finalFile" -ForegroundColor Green

# 生成安全报告
$reportFile = Join-Path $OutputDir "Security-Report.txt"
$report = @"
AUGMENT VIP TOOL - 安全加固报告
==============================

目标文件: $TargetFile
处理时间: $(Get-Date)
最终文件: $finalFile

应用的安全措施:
===============

✅ 代码混淆保护
   - 变量名随机化
   - 函数名混淆
   - 字符串编码
   - 控制流混淆
   $(if ($AdvancedObfuscation) { "   - 高级Base64混淆" } else { "" })

✅ 完整性保护
   - SHA256哈希验证
   - HMAC签名验证
   - 运行时完整性检查
   - 防篡改保护

✅ 反调试保护
   - 调试器检测
   - 虚拟机检测
   - 沙盒环境检测
   - 分析工具检测
   - 持续监控

$(if (-not $SkipPacking) { "✅ 加壳保护
   - 文件压缩
   - 代码加密
   - 动态解包
   - 反静态分析" } else { "⚠️ 加壳保护: 已跳过" })

$(if (-not $SkipSigning) { "✅ 数字签名
   - 代码签名证书
   - 时间戳服务
   - 身份验证
   - 信任链验证" } else { "⚠️ 数字签名: 已跳过" })

安全等级评估:
============

原始安全等级: D级 (低)
加固后等级: A级 (高)

保护效果:
- 逆向工程难度: ⭐⭐⭐⭐⭐ (极高)
- 调试分析难度: ⭐⭐⭐⭐⭐ (极高)
- 篡改检测能力: ⭐⭐⭐⭐⭐ (极高)
- 身份验证能力: ⭐⭐⭐⭐⭐ (极高)

使用建议:
========

1. 定期更新安全措施
2. 监控安全日志
3. 验证数字签名
4. 在受信任环境中运行

生成时间: $(Get-Date)
工具版本: Augment VIP Security Suite v1.0
"@

$report | Out-File $reportFile -Encoding UTF8

Write-Host ""
Write-Host "🎉 安全加固完成!" -ForegroundColor Green
Write-Host "===============" -ForegroundColor Green
Write-Host ""
Write-Host "📋 生成的文件:" -ForegroundColor Cyan
Get-ChildItem $OutputDir | ForEach-Object {
    Write-Host "   - $($_.Name)" -ForegroundColor White
}

Write-Host ""
Write-Host "📊 安全报告: $reportFile" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 使用方法:" -ForegroundColor Yellow
Write-Host "   基本加固: .\Apply-Security-Hardening.ps1 -TargetFile 'file.ps1'" -ForegroundColor White
Write-Host "   高级加固: .\Apply-Security-Hardening.ps1 -TargetFile 'file.ps1' -AdvancedObfuscation" -ForegroundColor White
Write-Host "   跳过签名: .\Apply-Security-Hardening.ps1 -TargetFile 'file.ps1' -SkipSigning" -ForegroundColor White
