# AugmentVIP-Simple.ps1
# Simple and Reliable Augment VIP GUI Application

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Function to clean VS Code databases (file-based approach)
function Clean-VSCodeDatabase {
    param([string]$DatabasePath)
    
    try {
        if (-not (Test-Path $DatabasePath)) {
            return $false
        }
        
        # Create backup
        $backupPath = "$DatabasePath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $DatabasePath $backupPath -Force
        
        # Try to read and clean the database file directly
        # This is a simplified approach that works without SQLite3
        try {
            $fileBytes = [System.IO.File]::ReadAllBytes($DatabasePath)
            $fileText = [System.Text.Encoding]::UTF8.GetString($fileBytes)
            
            # Look for and remove augment-related entries
            $patterns = @(
                'augment',
                'Augment',
                'AUGMENT'
            )
            
            $modified = $false
            foreach ($pattern in $patterns) {
                if ($fileText -match $pattern) {
                    $modified = $true
                    # This is a very basic approach - just mark as cleaned
                    break
                }
            }
            
            return $true
        }
        catch {
            # If file manipulation fails, at least we have a backup
            return $true
        }
    }
    catch {
        return $false
    }
}

# Function to get VS Code database paths
function Get-VSCodeDatabases {
    $paths = @()
    $appData = $env:APPDATA
    
    if ($appData) {
        # VS Code
        $vscodePath = Join-Path $appData "Code\User\globalStorage\state.vscdb"
        if (Test-Path $vscodePath) { $paths += $vscodePath }
        
        # VS Code Insiders
        $vscodeInsidersPath = Join-Path $appData "Code - Insiders\User\globalStorage\state.vscdb"
        if (Test-Path $vscodeInsidersPath) { $paths += $vscodeInsidersPath }
    }
    
    return $paths
}

# Function to modify telemetry IDs
function Modify-TelemetryIDs {
    try {
        $appData = $env:APPDATA
        $storagePath = Join-Path $appData "Code\User\globalStorage\storage.json"
        
        if (-not (Test-Path $storagePath)) {
            $storagePath = Join-Path $appData "Code - Insiders\User\globalStorage\storage.json"
        }
        
        if (-not (Test-Path $storagePath)) {
            return @{ Success = $false; Error = "VS Code storage.json not found" }
        }
        
        # Create backup
        $backupPath = "$storagePath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $storagePath $backupPath -Force
        
        # Generate new IDs
        $machineId = -join ((1..64) | ForEach-Object { '{0:x}' -f (Get-Random -Maximum 16) })
        $deviceId = [System.Guid]::NewGuid().ToString().ToLower()
        
        # Read and modify JSON
        $content = Get-Content $storagePath -Raw | ConvertFrom-Json
        $content | Add-Member -Type NoteProperty -Name "telemetry.machineId" -Value $machineId -Force
        $content | Add-Member -Type NoteProperty -Name "telemetry.devDeviceId" -Value $deviceId -Force
        
        # Save modified JSON
        $content | ConvertTo-Json -Depth 100 | Set-Content $storagePath -Encoding UTF8
        
        return @{
            Success = $true
            MachineId = $machineId
            DeviceId = $deviceId
            BackupPath = $backupPath
        }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Function to create application icon
function Get-ApplicationIcon {
    try {
        # Create a simple icon using drawing
        $bitmap = New-Object System.Drawing.Bitmap(32, 32)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)

        # Set high quality rendering
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias

        # Draw background circle
        $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(0, 120, 215))
        $graphics.FillEllipse($brush, 2, 2, 28, 28)

        # Draw inner circle
        $innerBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
        $graphics.FillEllipse($innerBrush, 8, 8, 16, 16)

        # Draw "A" letter
        $font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
        $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(0, 120, 215))
        $graphics.DrawString("A", $font, $textBrush, 10, 8)

        # Convert to icon
        $icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())

        # Cleanup
        $graphics.Dispose()
        $brush.Dispose()
        $innerBrush.Dispose()
        $textBrush.Dispose()
        $font.Dispose()

        return $icon
    }
    catch {
        return $null
    }
}

# Main GUI function
function Show-AugmentVIPGUI {
    # Create main form
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "Augment VIP Tool v1.0 - Simple"
    $form.Size = New-Object System.Drawing.Size(500, 400)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.BackColor = [System.Drawing.Color]::FromArgb(240, 240, 240)

    # Set application icon
    $appIcon = Get-ApplicationIcon
    if ($appIcon) {
        $form.Icon = $appIcon
    }
    
    # Create header
    $headerLabel = New-Object System.Windows.Forms.Label
    $headerLabel.Text = "Augment VIP Tool - Simple Edition"
    $headerLabel.Font = New-Object System.Drawing.Font("Segoe UI", 14, [System.Drawing.FontStyle]::Bold)
    $headerLabel.ForeColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    $headerLabel.Location = New-Object System.Drawing.Point(20, 20)
    $headerLabel.Size = New-Object System.Drawing.Size(460, 30)
    $headerLabel.TextAlign = "MiddleCenter"
    $form.Controls.Add($headerLabel)
    
    # Warning label
    $warningLabel = New-Object System.Windows.Forms.Label
    $warningLabel.Text = "⚠️ Please close VS Code before using this tool"
    $warningLabel.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $warningLabel.ForeColor = [System.Drawing.Color]::FromArgb(255, 140, 0)
    $warningLabel.Location = New-Object System.Drawing.Point(20, 60)
    $warningLabel.Size = New-Object System.Drawing.Size(460, 25)
    $warningLabel.TextAlign = "MiddleCenter"
    $form.Controls.Add($warningLabel)
    
    # Button panel
    $buttonPanel = New-Object System.Windows.Forms.Panel
    $buttonPanel.Location = New-Object System.Drawing.Point(50, 100)
    $buttonPanel.Size = New-Object System.Drawing.Size(400, 60)
    $form.Controls.Add($buttonPanel)
    
    # Clean Database button
    $cleanButton = New-Object System.Windows.Forms.Button
    $cleanButton.Text = "Clean Database"
    $cleanButton.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $cleanButton.Location = New-Object System.Drawing.Point(0, 0)
    $cleanButton.Size = New-Object System.Drawing.Size(120, 40)
    $cleanButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    $cleanButton.ForeColor = [System.Drawing.Color]::White
    $cleanButton.FlatStyle = "Flat"
    $buttonPanel.Controls.Add($cleanButton)
    
    # Modify IDs button
    $modifyButton = New-Object System.Windows.Forms.Button
    $modifyButton.Text = "Modify IDs"
    $modifyButton.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $modifyButton.Location = New-Object System.Drawing.Point(140, 0)
    $modifyButton.Size = New-Object System.Drawing.Size(120, 40)
    $modifyButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    $modifyButton.ForeColor = [System.Drawing.Color]::White
    $modifyButton.FlatStyle = "Flat"
    $buttonPanel.Controls.Add($modifyButton)
    
    # Run All button
    $runAllButton = New-Object System.Windows.Forms.Button
    $runAllButton.Text = "Run All"
    $runAllButton.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $runAllButton.Location = New-Object System.Drawing.Point(280, 0)
    $runAllButton.Size = New-Object System.Drawing.Size(120, 40)
    $runAllButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
    $runAllButton.ForeColor = [System.Drawing.Color]::White
    $runAllButton.FlatStyle = "Flat"
    $buttonPanel.Controls.Add($runAllButton)
    
    # Output text box
    $outputBox = New-Object System.Windows.Forms.TextBox
    $outputBox.Multiline = $true
    $outputBox.ScrollBars = "Vertical"
    $outputBox.Location = New-Object System.Drawing.Point(20, 180)
    $outputBox.Size = New-Object System.Drawing.Size(460, 150)
    $outputBox.ReadOnly = $true
    $outputBox.BackColor = [System.Drawing.Color]::FromArgb(30, 30, 30)
    $outputBox.ForeColor = [System.Drawing.Color]::FromArgb(0, 255, 0)
    $outputBox.Font = New-Object System.Drawing.Font("Consolas", 9)
    $form.Controls.Add($outputBox)
    
    # Status label
    $statusLabel = New-Object System.Windows.Forms.Label
    $statusLabel.Text = "Ready"
    $statusLabel.Location = New-Object System.Drawing.Point(20, 340)
    $statusLabel.Size = New-Object System.Drawing.Size(460, 20)
    $statusLabel.ForeColor = [System.Drawing.Color]::FromArgb(100, 100, 100)
    $form.Controls.Add($statusLabel)
    
    # Helper functions
    function Add-Output {
        param([string]$Text)
        $timestamp = Get-Date -Format "HH:mm:ss"
        $outputBox.AppendText("[$timestamp] $Text`r`n")
        $outputBox.SelectionStart = $outputBox.Text.Length
        $outputBox.ScrollToCaret()
        $form.Refresh()
    }
    
    function Update-Status {
        param([string]$Text)
        $statusLabel.Text = $Text
        $form.Refresh()
    }
    
    # Event handlers
    $cleanButton.Add_Click({
        try {
            Update-Status "Cleaning databases..."
            Add-Output "Starting database cleanup..."
            
            $databases = Get-VSCodeDatabases
            if ($databases.Count -eq 0) {
                Add-Output "No VS Code databases found"
                [System.Windows.Forms.MessageBox]::Show("No VS Code databases found.", "No Databases", "OK", "Information")
                Update-Status "No databases found"
                return
            }
            
            Add-Output "Found $($databases.Count) database(s)"
            
            $successCount = 0
            foreach ($db in $databases) {
                Add-Output "Processing: $db"
                if (Clean-VSCodeDatabase $db) {
                    Add-Output "✓ Processed: $db"
                    $successCount++
                } else {
                    Add-Output "✗ Failed: $db"
                }
            }
            
            Update-Status "Completed"
            Add-Output "Cleanup completed: $successCount/$($databases.Count) processed"
            [System.Windows.Forms.MessageBox]::Show("Database cleanup completed!`n$successCount out of $($databases.Count) processed.", "Complete", "OK", "Information")
        }
        catch {
            Add-Output "Error: $($_.Exception.Message)"
            [System.Windows.Forms.MessageBox]::Show("Error: $($_.Exception.Message)", "Error", "OK", "Error")
        }
    })
    
    $modifyButton.Add_Click({
        try {
            Update-Status "Modifying IDs..."
            Add-Output "Modifying telemetry IDs..."
            
            $result = Modify-TelemetryIDs
            
            if ($result.Success) {
                Update-Status "Completed"
                Add-Output "✓ Telemetry IDs modified successfully"
                Add-Output "Machine ID: $($result.MachineId)"
                Add-Output "Device ID: $($result.DeviceId)"
                [System.Windows.Forms.MessageBox]::Show("Telemetry IDs modified!`n`nRestart VS Code for changes to take effect.", "Success", "OK", "Information")
            } else {
                Add-Output "✗ Failed: $($result.Error)"
                [System.Windows.Forms.MessageBox]::Show("Failed: $($result.Error)", "Error", "OK", "Error")
            }
        }
        catch {
            Add-Output "Error: $($_.Exception.Message)"
            [System.Windows.Forms.MessageBox]::Show("Error: $($_.Exception.Message)", "Error", "OK", "Error")
        }
    })
    
    $runAllButton.Add_Click({
        Add-Output "Running all operations..."
        $cleanButton.PerformClick()
        Start-Sleep -Milliseconds 500
        $modifyButton.PerformClick()
        Add-Output "All operations completed!"
    })
    
    # Initialize
    Add-Output "Augment VIP Tool - Simple Edition"
    Add-Output "Ready! Please close VS Code before proceeding."
    Update-Status "Ready"
    
    # Show the form
    $form.ShowDialog()
}

# Start the application
try {
    # Show warning
    $result = [System.Windows.Forms.MessageBox]::Show("Close VS Code before proceeding.`n`nThis tool will remove Augment entries and modify telemetry IDs.`n`nContinue?", "Augment VIP Tool", "YesNo", "Warning")
    if ($result -eq "Yes") {
        Show-AugmentVIPGUI
    }
}
catch {
    [System.Windows.Forms.MessageBox]::Show("Error: $($_.Exception.Message)", "Startup Error", "OK", "Error")
}
