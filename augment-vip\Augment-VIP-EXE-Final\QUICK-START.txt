AUGMENT VIP TOOL - <PERSON><PERSON><PERSON>K START
==============================

🚀 FASTEST WAY TO USE:

1. CLOSE VS CODE (Important!)
2. Double-click "AugmentVIP-Enhanced.exe"
3. Click "Yes" to continue
4. Click "🚀 Run All" button
5. Wait for "All operations completed!"
6. Restart VS Code

✅ DONE! Your VS Code is now cleaned.

📋 STEP-BY-STEP GUIDE:

PREPARATION:
□ Close VS Code completely
□ Close VS Code Insiders (if installed)
□ Make sure no VS Code processes are running

EXECUTION:
□ Double-click AugmentVIP-Enhanced.exe (recommended)
  OR AugmentVIP-Standalone.exe (basic version)
□ Click "Yes" when asked about closing VS Code
□ Choose operation:
  - "Clean Database" = Remove Augment entries only
  - "Modify IDs" = Change telemetry IDs only  
  - "Run All" = Do both (recommended)
□ Watch the green text output for progress
□ Wait for completion message

COMPLETION:
□ Click "OK" on success message
□ Close the tool
□ Restart VS Code
□ Verify VS Code works normally

⚠️ TROUBLESHOOTING:

Problem: EXE won't start
Solution: Right-click → "Run as administrator"

Problem: Windows security warning
Solution: Click "More info" → "Run anyway"

Problem: "No databases found"
Solution: Make sure VS Code was installed and run at least once

Problem: Permission denied
Solution: Run as administrator, close all VS Code processes

Problem: SQLite3 initialization failed
Solution: Tool will still work with backup methods

🎯 WHICH VERSION TO USE:

AugmentVIP-Enhanced.exe (RECOMMENDED):
• Better user interface
• More detailed feedback
• Enhanced error handling
• Progress indicators

AugmentVIP-Standalone.exe:
• Simpler interface
• Faster startup
• Good for quick operations

💡 TIPS:

• Always close VS Code first!
• Run as Administrator if you have issues
• Check the green output text for details
• Backup files are created automatically
• Restart VS Code after completion

🔄 WHAT HAPPENS:

The tool will:
1. Create backups of your VS Code files
2. Remove Augment-related database entries
3. Generate new random telemetry IDs
4. Show you exactly what was changed

Your VS Code settings and extensions remain unchanged.
Only Augment entries and telemetry IDs are modified.

📞 NEED HELP?

Check README.txt for detailed information.
All operations are logged in the green output area.

🎉 That's it! Enjoy your cleaned VS Code!
