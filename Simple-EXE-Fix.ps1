# 简化的EXE修复脚本
Write-Host "修复EXE创建问题..." -ForegroundColor Cyan

# 检查编译器
$compiler = "$env:WINDIR\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
if (-not (Test-Path $compiler)) {
    $compiler = "$env:WINDIR\Microsoft.NET\Framework\v4.0.30319\csc.exe"
}

if (Test-Path $compiler) {
    Write-Host "找到编译器: $compiler" -ForegroundColor Green
    
    # 创建简单的C#代码
    $csharpCode = @'
using System;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using System.IO;

class Program
{
    static void Main()
    {
        try
        {
            Console.OutputEncoding = Encoding.UTF8;
            
            Console.WriteLine();
            Console.WriteLine("================================================================");
            Console.WriteLine("                AUGMENT VIP 工具 - 企业安全版                  ");
            Console.WriteLine("================================================================");
            Console.WriteLine("产品名称: Augment VIP 工具企业安全版");
            Console.WriteLine("版本信息: v2.0 中文EXE版");
            Console.WriteLine("保护等级: 需要许可证密钥");
            Console.WriteLine("================================================================");
            Console.WriteLine();
            Console.WriteLine("请输入您的许可证密钥以继续使用:");
            Console.WriteLine("（许可证密钥区分大小写，请准确输入）");
            Console.WriteLine();
            Console.Write("许可证密钥: ");
            
            string userInput = Console.ReadLine();
            
            Console.WriteLine();
            Console.WriteLine("正在验证许可证...");
            
            if (ValidateLicense(userInput))
            {
                Console.WriteLine();
                Console.WriteLine("================================================================");
                Console.WriteLine("                      许可证验证成功                           ");
                Console.WriteLine("================================================================");
                Console.WriteLine("状态信息: 许可证密钥有效");
                Console.WriteLine("授权状态: 已获得授权");
                Console.WriteLine($"验证时间: {DateTime.Now:yyyy年MM月dd日 HH:mm:ss}");
                Console.WriteLine($"用户信息: {Environment.UserName}@{Environment.MachineName}");
                Console.WriteLine("================================================================");
                Console.WriteLine();
                
                LogAccess(true, userInput);
                
                Console.WriteLine("正在启动应用程序...");
                
                if (Debugger.IsAttached)
                {
                    Console.WriteLine("[安全警告] 检测到调试器附加!");
                    Console.WriteLine("[安全警告] 因安全原因，应用程序将被终止。");
                    System.Threading.Thread.Sleep(3000);
                    Environment.Exit(1);
                }
                
                Console.WriteLine("[安全检查] 安全检查通过。正在启动应用程序...");
                
                LaunchPowerShellScript();
            }
            else
            {
                Console.WriteLine();
                Console.WriteLine("================================================================");
                Console.WriteLine("                      许可证验证失败                           ");
                Console.WriteLine("================================================================");
                Console.WriteLine("错误信息: 无效的许可证密钥");
                Console.WriteLine("解决方案: 请联系软件提供商获取有效的许可证");
                Console.WriteLine("技术支持: <EMAIL>");
                Console.WriteLine("警告信息: 未经授权使用本软件是违法行为");
                Console.WriteLine("================================================================");
                Console.WriteLine();
                
                LogAccess(false, userInput);
                
                Console.WriteLine("应用程序将在3秒后退出...");
                System.Threading.Thread.Sleep(3000);
                Environment.Exit(1);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误: {ex.Message}");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
    
    static bool ValidateLicense(string inputKey)
    {
        try
        {
            if (string.IsNullOrEmpty(inputKey))
                return false;
            
            string expectedHash = "db42154cfde3cabadf3521ef55f88a74e8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8";
            
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] keyBytes = Encoding.UTF8.GetBytes(inputKey.Trim());
                byte[] hashBytes = sha256.ComputeHash(keyBytes);
                string inputHash = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
                
                return inputHash.Equals(expectedHash, StringComparison.OrdinalIgnoreCase);
            }
        }
        catch
        {
            return false;
        }
    }
    
    static void LaunchPowerShellScript()
    {
        try
        {
            ProcessStartInfo psi = new ProcessStartInfo();
            psi.FileName = "powershell.exe";
            psi.Arguments = "-ExecutionPolicy Bypass -File \"AugmentVIP-LICENSED-SECURE.ps1\"";
            psi.UseShellExecute = false;
            psi.CreateNoWindow = false;
            
            Process process = Process.Start(psi);
            if (process != null)
            {
                process.WaitForExit();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"启动PowerShell脚本失败: {ex.Message}");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
    
    static void LogAccess(bool authorized, string input)
    {
        try
        {
            string logDir = "logs";
            if (!Directory.Exists(logDir))
                Directory.CreateDirectory(logDir);
            
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            string logEntry;
            string logFile;
            
            if (authorized)
            {
                logEntry = $"{timestamp} - 授权访问 - 用户: {Environment.UserName} - 计算机: {Environment.MachineName}";
                logFile = Path.Combine(logDir, "license-access.log");
            }
            else
            {
                string inputPreview = input.Length > 10 ? input.Substring(0, 10) + "..." : input;
                logEntry = $"{timestamp} - 未授权访问尝试 - 用户: {Environment.UserName} - 计算机: {Environment.MachineName} - 输入: {inputPreview}";
                logFile = Path.Combine(logDir, "license-violations.log");
            }
            
            File.AppendAllText(logFile, logEntry + Environment.NewLine, Encoding.UTF8);
        }
        catch
        {
            // 忽略日志错误
        }
    }
}
'@
    
    # 保存C#代码
    $csharpCode | Out-File "SimpleAugmentVIP.cs" -Encoding UTF8
    
    # 编译
    $outputFile = "Augment-VIP-ENTERPRISE-SECURITY\AugmentVIP-中文安全版.exe"
    $args = @("/out:$outputFile", "/target:exe", "/optimize+", "SimpleAugmentVIP.cs")
    
    Write-Host "正在编译EXE..." -ForegroundColor Yellow
    $process = Start-Process -FilePath $compiler -ArgumentList $args -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "EXE编译成功!" -ForegroundColor Green
        
        $fileInfo = Get-Item $outputFile
        Write-Host "文件: $($fileInfo.Name)" -ForegroundColor White
        Write-Host "大小: $($fileInfo.Length) bytes" -ForegroundColor White
        
    } else {
        Write-Host "编译失败，退出代码: $($process.ExitCode)" -ForegroundColor Red
    }
    
    # 清理
    Remove-Item "SimpleAugmentVIP.cs" -Force -ErrorAction SilentlyContinue
    
} else {
    Write-Host "未找到.NET编译器" -ForegroundColor Red
}

# 创建批处理文件
Write-Host "创建批处理文件..." -ForegroundColor Yellow

$batchContent = @'
@echo off
chcp 65001 >nul
title Augment VIP 工具 - 中文安全版

echo.
echo ================================================================
echo                 AUGMENT VIP 工具 - 企业安全版                  
echo ================================================================
echo 产品名称: Augment VIP 工具企业安全版
echo 版本信息: v2.0 中文批处理版
echo 保护等级: 需要许可证密钥
echo ================================================================
echo.

powershell -ExecutionPolicy Bypass -File "AugmentVIP-LICENSED-SECURE.ps1"

pause
'@

$batchContent | Out-File "Augment-VIP-ENTERPRISE-SECURITY\AugmentVIP-中文安全版.bat" -Encoding UTF8

Write-Host "批处理文件已创建" -ForegroundColor Green

Write-Host ""
Write-Host "修复完成!" -ForegroundColor Green
Write-Host "可用文件:" -ForegroundColor Cyan
Write-Host "1. AugmentVIP-中文安全版.exe (如果编译成功)" -ForegroundColor White
Write-Host "2. AugmentVIP-中文安全版.bat (批处理版本)" -ForegroundColor White
