# Augment VIP 使用指南

## 🎉 安装成功！

恭喜您已经成功安装了Augment VIP项目！这个工具可以帮助您：

1. **清理VS Code数据库** - 移除Augment相关的条目
2. **修改遥测ID** - 生成随机的遥测标识符以增强隐私

## 📁 项目结构

```
augment-vip/
├── config/                 # 配置文件目录
│   └── config.json         # 项目配置
├── data/                   # 数据存储目录
├── logs/                   # 日志文件目录
├── temp/                   # 临时文件目录
├── scripts/                # 脚本目录
│   ├── clean_code_db.ps1   # PowerShell数据库清理脚本
│   ├── id_modifier.ps1     # PowerShell ID修改脚本
│   ├── clean_code_db.sh    # 原始Bash脚本
│   ├── id_modifier.sh      # 原始Bash脚本
│   └── install.sh          # 原始Bash安装脚本
├── sqlite3.exe             # SQLite3可执行文件（自动下载）
├── install.ps1             # PowerShell安装脚本
├── install.sh              # 原始Bash安装脚本
├── README.md               # 原始项目说明
├── README_PowerShell.md    # PowerShell版本说明
└── 使用指南.md             # 本文件
```

## 🚀 快速使用

### 方法1：使用安装脚本（推荐）

```powershell
# 运行所有功能
.\install.ps1 -All

# 只清理数据库
.\install.ps1 -Clean

# 只修改遥测ID
.\install.ps1 -ModifyIds

# 查看帮助
.\install.ps1 -Help
```

### 方法2：单独运行脚本

```powershell
# 清理VS Code数据库
.\scripts\clean_code_db.ps1

# 修改VS Code遥测ID
.\scripts\id_modifier.ps1

# 查看脚本帮助
.\scripts\clean_code_db.ps1 -Help
.\scripts\id_modifier.ps1 -Help
```

## 🔧 功能说明

### 数据库清理功能

- **作用**: 从VS Code数据库中移除包含"augment"的条目
- **支持**: VS Code 和 VS Code Insiders
- **安全**: 自动创建备份文件
- **依赖**: 自动下载SQLite3（如果需要）

### 遥测ID修改功能

- **作用**: 修改VS Code的遥测标识符
- **生成**: 随机64字符机器ID和UUID设备ID
- **位置**: 修改storage.json文件
- **安全**: 自动创建备份文件

## ⚠️ 重要提示

1. **运行前关闭VS Code**: 确保VS Code完全关闭后再运行脚本
2. **备份文件**: 所有操作都会自动创建备份文件
3. **重启生效**: 修改后需要重启VS Code才能生效
4. **管理员权限**: 如果遇到权限问题，请以管理员身份运行PowerShell

## 🛠️ 故障排除

### 执行策略问题
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 权限问题
- 右键点击PowerShell，选择"以管理员身份运行"

### VS Code未找到
- 确保VS Code已安装并至少运行过一次

## 📝 使用示例

### 完整清理流程
```powershell
# 1. 关闭VS Code
# 2. 运行完整清理
.\install.ps1 -All
# 3. 重启VS Code
```

### 仅清理数据库
```powershell
# 1. 关闭VS Code
# 2. 清理数据库
.\scripts\clean_code_db.ps1
# 3. 重启VS Code
```

### 仅修改遥测ID
```powershell
# 1. 关闭VS Code
# 2. 修改遥测ID
.\scripts\id_modifier.ps1
# 3. 重启VS Code
```

## 🔍 验证结果

### 检查数据库清理结果
脚本会显示清理的条目数量和操作状态。

### 检查遥测ID修改结果
脚本会显示新生成的机器ID和设备ID。

## 📞 获取帮助

如果遇到问题，可以：

1. 查看脚本帮助信息：
   ```powershell
   .\install.ps1 -Help
   .\scripts\clean_code_db.ps1 -Help
   .\scripts\id_modifier.ps1 -Help
   ```

2. 查看详细文档：
   - `README_PowerShell.md` - PowerShell版本详细说明
   - `README.md` - 原始项目说明

3. 访问原项目：
   - GitHub: https://github.com/azrilaiman2003/augment-vip

## 🎯 使用建议

1. **定期清理**: 建议定期运行清理脚本
2. **备份重要数据**: 虽然脚本会自动备份，但建议您也手动备份重要配置
3. **测试环境**: 如果不确定，可以先在测试环境中运行

---

**注意**: 本工具仅用于教育和研究目的，请遵守相关法律法规和软件使用条款。
