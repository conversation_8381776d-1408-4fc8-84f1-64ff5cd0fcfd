# 打包工具.ps1
# 用于创建 Augment VIP 一键运行软件包

param(
    [string]$OutputPath = ".\Augment-VIP-Package",
    [switch]$Help
)

if ($Help) {
    Write-Host "Augment VIP 打包工具" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法: .\打包工具.ps1 [-OutputPath <路径>]"
    Write-Host ""
    Write-Host "参数:"
    Write-Host "  -OutputPath    输出路径 (默认: .\Augment-VIP-Package)"
    Write-Host "  -Help          显示此帮助信息"
    Write-Host ""
    exit 0
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "INFO" { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Blue }
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green }
        "WARNING" { Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor Yellow }
        "ERROR" { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red }
    }
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Augment VIP 软件包打包工具        " -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Log "开始创建软件包..." "INFO"

# 创建输出目录
if (Test-Path $OutputPath) {
    Write-Log "清理现有输出目录..." "WARNING"
    Remove-Item $OutputPath -Recurse -Force
}

New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
Write-Log "创建输出目录: $OutputPath" "SUCCESS"

# 定义需要包含的文件和目录
$includeItems = @(
    "scripts\clean_code_db.ps1",
    "scripts\id_modifier.ps1",
    "scripts\clean_code_db.sh",
    "scripts\id_modifier.sh",
    "scripts\install.sh",
    "install.ps1",
    "install.sh",
    "一键运行.bat",
    "自动安装.bat",
    "README.md",
    "README_PowerShell.md",
    "使用指南.md",
    "config\config.json"
)

# 可选文件（如果存在则包含）
$optionalItems = @(
    "sqlite3.exe"
)

# 复制必需文件
Write-Log "复制项目文件..." "INFO"

foreach ($item in $includeItems) {
    $sourcePath = Join-Path (Get-Location) $item
    $destPath = Join-Path $OutputPath $item
    
    if (Test-Path $sourcePath) {
        # 确保目标目录存在
        $destDir = Split-Path $destPath -Parent
        if (-not (Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        
        Copy-Item $sourcePath $destPath -Force
        Write-Log "已复制: $item" "SUCCESS"
    } else {
        Write-Log "文件未找到: $item" "WARNING"
    }
}

# 复制可选文件
foreach ($item in $optionalItems) {
    $sourcePath = Join-Path (Get-Location) $item
    $destPath = Join-Path $OutputPath $item
    
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $destPath -Force
        Write-Log "已复制可选文件: $item" "SUCCESS"
    }
}

# 创建空目录
$emptyDirs = @("logs", "data", "temp")
foreach ($dir in $emptyDirs) {
    $dirPath = Join-Path $OutputPath $dir
    New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
    Write-Log "创建目录: $dir" "SUCCESS"
}

# 创建安装说明文件
$installInstructions = @'
# Augment VIP 一键运行软件包

## 快速开始

### 方法1: 自动安装（推荐）
1. 双击运行 自动安装.bat
2. 按照提示完成安装
3. 使用 一键运行.bat 开始使用

### 方法2: 手动安装
1. 双击运行 一键运行.bat
2. 选择相应功能

## 系统要求
- Windows 10/11
- PowerShell 5.1 或更高版本
- VS Code 已安装

## 重要提示
- 使用前请关闭 VS Code
- 修改后需要重启 VS Code
- 所有操作都会自动备份
- 本工具仅用于教育和研究目的

## 文件说明
- 自动安装.bat - 自动安装程序
- 一键运行.bat - 主程序入口
- install.ps1 - PowerShell 安装脚本
- scripts/ - 功能脚本目录
- 使用指南.md - 详细使用说明

## 功能说明
1. 数据库清理: 移除 VS Code 中的 Augment 相关条目
2. 遥测ID修改: 生成随机遥测标识符

如有问题，请查看 使用指南.md 获取详细帮助。
'@

$installInstructions | Set-Content (Join-Path $OutputPath "安装说明.txt") -Encoding UTF8
Write-Log "创建安装说明文件" "SUCCESS"

# 创建版本信息文件
$versionInfo = @{
    version = "1.0.0"
    build_date = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    platform = "Windows"
    type = "Portable Package"
    features = @("Database Cleaning", "Telemetry ID Modification")
}

$versionInfo | ConvertTo-Json -Depth 3 | Set-Content (Join-Path $OutputPath "version.json") -Encoding UTF8
Write-Log "创建版本信息文件" "SUCCESS"

# 创建压缩包
$zipPath = "$OutputPath.zip"
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
}

Write-Log "创建压缩包..." "INFO"
try {
    Compress-Archive -Path "$OutputPath\*" -DestinationPath $zipPath -Force
    Write-Log "压缩包已创建: $zipPath" "SUCCESS"
} catch {
    Write-Log "创建压缩包失败: $($_.Exception.Message)" "ERROR"
}

# 计算文件大小
$packageSize = (Get-Item $OutputPath | Get-ChildItem -Recurse | Measure-Object -Property Length -Sum).Sum
$zipSize = if (Test-Path $zipPath) { (Get-Item $zipPath).Length } else { 0 }

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "         打包完成！                     " -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Log "软件包目录: $OutputPath" "INFO"
Write-Log "软件包大小: $([math]::Round($packageSize/1MB, 2)) MB" "INFO"
if ($zipSize -gt 0) {
    Write-Log "压缩包文件: $zipPath" "INFO"
    Write-Log "压缩包大小: $([math]::Round($zipSize/1MB, 2)) MB" "INFO"
}
Write-Host ""
Write-Host "您现在可以:" -ForegroundColor Yellow
Write-Host "1. 分发整个 $OutputPath 目录" -ForegroundColor Yellow
Write-Host "2. 分发压缩包 $zipPath" -ForegroundColor Yellow
Write-Host "3. 用户只需解压并运行 自动安装.bat" -ForegroundColor Yellow
