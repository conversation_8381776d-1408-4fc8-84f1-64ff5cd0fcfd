# create_package.ps1
# Simple packaging script for Augment VIP

param(
    [string]$OutputPath = ".\Augment-VIP-Package"
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    switch ($Level) {
        "INFO" { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Blue }
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green }
        "WARNING" { Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor Yellow }
        "ERROR" { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red }
    }
}

Write-Host "Augment VIP Package Creator" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan
Write-Host ""

Write-Log "Starting package creation..." "INFO"

# Clean and create output directory
if (Test-Path $OutputPath) {
    Remove-Item $OutputPath -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
Write-Log "Created output directory: $OutputPath" "SUCCESS"

# Copy essential files
$files = @(
    "scripts\clean_code_db.ps1",
    "scripts\id_modifier.ps1", 
    "scripts\clean_code_db.sh",
    "scripts\id_modifier.sh",
    "scripts\install.sh",
    "install.ps1",
    "install.sh",
    "一键运行.bat",
    "自动安装.bat",
    "README.md",
    "README_PowerShell.md",
    "使用指南.md"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        $destDir = Split-Path (Join-Path $OutputPath $file) -Parent
        if (-not (Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        Copy-Item $file (Join-Path $OutputPath $file) -Force
        Write-Log "Copied: $file" "SUCCESS"
    } else {
        Write-Log "File not found: $file" "WARNING"
    }
}

# Copy optional files
if (Test-Path "sqlite3.exe") {
    Copy-Item "sqlite3.exe" (Join-Path $OutputPath "sqlite3.exe") -Force
    Write-Log "Copied: sqlite3.exe" "SUCCESS"
}

if (Test-Path "config\config.json") {
    $configDir = Join-Path $OutputPath "config"
    New-Item -ItemType Directory -Path $configDir -Force | Out-Null
    Copy-Item "config\config.json" (Join-Path $configDir "config.json") -Force
    Write-Log "Copied: config\config.json" "SUCCESS"
}

# Create empty directories
$dirs = @("logs", "data", "temp")
foreach ($dir in $dirs) {
    $dirPath = Join-Path $OutputPath $dir
    New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
    Write-Log "Created directory: $dir" "SUCCESS"
}

# Create installation instructions
$instructions = "Augment VIP Package Instructions

Quick Start:
1. Run 自动安装.bat for automatic installation
2. Use 一键运行.bat to access the main interface

System Requirements:
- Windows 10/11
- PowerShell 5.1+
- VS Code installed

Important Notes:
- Close VS Code before running
- Restart VS Code after modifications
- All operations create automatic backups
- For educational purposes only

Files:
- 自动安装.bat: Automatic installer
- 一键运行.bat: Main program interface
- install.ps1: PowerShell installation script
- scripts/: Function scripts directory
- 使用指南.md: Detailed usage guide

For help, see 使用指南.md"

$instructions | Set-Content (Join-Path $OutputPath "INSTRUCTIONS.txt") -Encoding UTF8
Write-Log "Created instructions file" "SUCCESS"

# Create version info
$version = @{
    version = "1.0.0"
    build_date = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    platform = "Windows"
    type = "Portable"
} | ConvertTo-Json

$version | Set-Content (Join-Path $OutputPath "version.json") -Encoding UTF8
Write-Log "Created version file" "SUCCESS"

# Create ZIP archive
$zipPath = "$OutputPath.zip"
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
}

try {
    Compress-Archive -Path "$OutputPath\*" -DestinationPath $zipPath -Force
    Write-Log "Created ZIP archive: $zipPath" "SUCCESS"
} catch {
    Write-Log "Failed to create ZIP: $($_.Exception.Message)" "ERROR"
}

# Calculate sizes
$packageSize = (Get-ChildItem $OutputPath -Recurse | Measure-Object -Property Length -Sum).Sum
$zipSize = if (Test-Path $zipPath) { (Get-Item $zipPath).Length } else { 0 }

Write-Host ""
Write-Host "Package Creation Complete!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""
Write-Log "Package directory: $OutputPath" "INFO"
Write-Log "Package size: $([math]::Round($packageSize/1MB, 2)) MB" "INFO"
if ($zipSize -gt 0) {
    Write-Log "ZIP file: $zipPath" "INFO"
    Write-Log "ZIP size: $([math]::Round($zipSize/1MB, 2)) MB" "INFO"
}
Write-Host ""
Write-Host "Distribution options:" -ForegroundColor Yellow
Write-Host "1. Share the $OutputPath directory" -ForegroundColor Yellow
Write-Host "2. Share the ZIP file $zipPath" -ForegroundColor Yellow
Write-Host "3. Users run 自动安装.bat after extraction" -ForegroundColor Yellow
