# Augment VIP - 一键运行软件包

## 🎉 欢迎使用 Augment VIP 一键运行软件包！

这是一个完整的、开箱即用的 Augment VIP 工具包，专为 Windows 用户设计。无需复杂的安装过程，解压即可使用！

## 📦 软件包内容

### 🚀 一键运行工具
- **`OneClick-Run.bat`** - 英文界面一键运行工具（推荐）
- **`Auto-Install.bat`** - 英文自动安装程序
- **`一键运行.bat`** - 中文界面一键运行工具
- **`自动安装.bat`** - 中文自动安装程序

### 🔧 PowerShell 脚本
- **`install.ps1`** - PowerShell 安装脚本
- **`scripts/clean_code_db.ps1`** - 数据库清理脚本
- **`scripts/id_modifier.ps1`** - 遥测ID修改脚本

### 📚 文档和配置
- **`README-Package.md`** - 本文件（软件包说明）
- **`使用指南.md`** - 详细中文使用指南
- **`README_PowerShell.md`** - PowerShell版本详细说明
- **`INSTRUCTIONS.txt`** - 英文快速说明
- **`config/config.json`** - 配置文件

### 🛠️ 依赖工具
- **`sqlite3.exe`** - SQLite3 数据库工具（已包含）

## 🚀 快速开始

### 方法1：使用英文界面（推荐）
1. **双击运行** `OneClick-Run.bat`
2. 选择需要的功能：
   - `[1]` 完整安装并运行所有功能
   - `[2]` 仅清理 VS Code 数据库
   - `[3]` 仅修改遥测 ID
   - `[4]` 查看帮助信息

### 方法2：使用中文界面
1. **双击运行** `一键运行.bat`
2. 按照菜单提示选择功能

### 方法3：自动安装
1. **双击运行** `Auto-Install.bat` 或 `自动安装.bat`
2. 按照提示完成自动安装和配置

## 🔧 功能说明

### 数据库清理功能
- **作用**：移除 VS Code 数据库中的 Augment 相关条目
- **支持**：VS Code 和 VS Code Insiders
- **安全**：自动创建备份文件
- **依赖**：自动使用包含的 SQLite3

### 遥测ID修改功能
- **作用**：修改 VS Code 的遥测标识符
- **生成**：随机64字符机器ID和UUID设备ID
- **位置**：修改 storage.json 文件
- **安全**：自动创建备份文件

## ⚠️ 重要提示

### 使用前准备
1. **关闭 VS Code**：确保 VS Code 完全关闭
2. **管理员权限**：如遇权限问题，右键"以管理员身份运行"
3. **杀毒软件**：可能需要将工具添加到杀毒软件白名单

### 使用后操作
1. **重启 VS Code**：修改后需要重启 VS Code 才能生效
2. **检查备份**：所有操作都会自动创建备份文件
3. **验证结果**：可以检查工具输出的日志信息

## 📋 系统要求

- **操作系统**：Windows 10/11
- **PowerShell**：5.1 或更高版本（通常已预装）
- **VS Code**：已安装并至少运行过一次
- **权限**：普通用户权限（某些情况下可能需要管理员权限）

## 🛠️ 故障排除

### 常见问题

**1. PowerShell 执行策略错误**
```
解决方案：以管理员身份运行 PowerShell，执行：
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**2. 找不到 VS Code 数据库**
```
原因：VS Code 未安装或未运行过
解决方案：确保 VS Code 已安装并至少启动过一次
```

**3. 权限被拒绝**
```
解决方案：右键点击批处理文件，选择"以管理员身份运行"
```

**4. 中文界面乱码**
```
解决方案：使用英文界面版本 OneClick-Run.bat
```

## 📁 目录结构

```
Augment-VIP-Package/
├── OneClick-Run.bat          # 英文一键运行工具
├── Auto-Install.bat          # 英文自动安装
├── 一键运行.bat              # 中文一键运行工具
├── 自动安装.bat              # 中文自动安装
├── install.ps1               # PowerShell安装脚本
├── sqlite3.exe               # SQLite3工具
├── scripts/                  # 脚本目录
│   ├── clean_code_db.ps1     # 数据库清理脚本
│   ├── id_modifier.ps1       # ID修改脚本
│   └── ...                   # 其他脚本
├── config/                   # 配置目录
│   └── config.json           # 配置文件
├── logs/                     # 日志目录
├── data/                     # 数据目录
├── temp/                     # 临时目录
└── 文档文件...               # 各种说明文档
```

## 🎯 使用建议

1. **首次使用**：建议使用"完整安装并运行所有功能"选项
2. **定期清理**：可以定期运行数据库清理功能
3. **备份重要数据**：虽然工具会自动备份，但建议手动备份重要配置
4. **测试环境**：如果不确定，可以先在测试环境中使用

## 📞 获取帮助

- **查看详细文档**：`使用指南.md`
- **PowerShell版本说明**：`README_PowerShell.md`
- **英文快速说明**：`INSTRUCTIONS.txt`
- **原项目地址**：https://github.com/azrilaiman2003/augment-vip

## 📜 免责声明

本工具仅用于教育和研究目的。使用者应当：
- 遵守相关法律法规
- 遵守软件使用条款
- 承担使用风险
- 不用于商业用途

---

**享受使用 Augment VIP 工具！** 🎉
