# Simple Code Signing Script
Write-Host "Creating Code Signing Certificate..." -ForegroundColor Cyan

try {
    # Check if certificate already exists
    $existingCert = Get-ChildItem -Path Cert:\CurrentUser\My | Where-Object { $_.Subject -like "*Augment VIP Tool*" }
    
    if ($existingCert) {
        Write-Host "Found existing certificate: $($existingCert.Thumbprint)" -ForegroundColor Green
        $cert = $existingCert
    } else {
        # Create new self-signed certificate
        $cert = New-SelfSignedCertificate -Type CodeSigningCert -Subject "CN=Augment VIP Tool, O=Augment Code, C=CN" -KeyUsage DigitalSignature -FriendlyName "Augment VIP Tool Code Signing" -CertStoreLocation Cert:\CurrentUser\My -KeyLength 2048 -Provider "Microsoft Enhanced RSA and AES Cryptographic Provider" -KeyExportPolicy Exportable -KeySpec Signature -KeyUsageProperty Sign -TextExtension @("*********={text}*******.*******.3", "*********={text}")
        
        Write-Host "Certificate created successfully!" -ForegroundColor Green
        Write-Host "Thumbprint: $($cert.Thumbprint)" -ForegroundColor Cyan
    }
    
    # Export certificate
    $certBytes = $cert.Export([System.Security.Cryptography.X509Certificates.X509ContentType]::Cert)
    [System.IO.File]::WriteAllBytes("AugmentVIP-CodeSigning.cer", $certBytes)
    Write-Host "Certificate exported: AugmentVIP-CodeSigning.cer" -ForegroundColor Green
    
    # Export with password
    $password = "AugmentVIP2025!"
    $pfxBytes = $cert.Export([System.Security.Cryptography.X509Certificates.X509ContentType]::Pfx, $password)
    [System.IO.File]::WriteAllBytes("AugmentVIP-CodeSigning.pfx", $pfxBytes)
    Write-Host "PFX exported: AugmentVIP-CodeSigning.pfx" -ForegroundColor Green
    Write-Host "Password: $password" -ForegroundColor Yellow
    
    # Sign all EXE files
    $exeFiles = Get-ChildItem -Path "." -Filter "*.exe" -Recurse | Where-Object { $_.Name -like "*AugmentVIP*" }
    
    foreach ($file in $exeFiles) {
        try {
            Write-Host "Signing: $($file.Name)..." -ForegroundColor Yellow
            $result = Set-AuthenticodeSignature -FilePath $file.FullName -Certificate $cert -TimestampServer "http://timestamp.digicert.com"
            
            if ($result.Status -eq "Valid") {
                Write-Host "  SUCCESS: $($file.Name) signed" -ForegroundColor Green
            } else {
                Write-Host "  WARNING: $($file.Name) status: $($result.Status)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  ERROR: $($file.Name) failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "Code signing completed!" -ForegroundColor Green
    
} catch {
    Write-Host "Code signing failed: $($_.Exception.Message)" -ForegroundColor Red
}
