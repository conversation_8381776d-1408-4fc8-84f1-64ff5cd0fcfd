# 修复EXE创建问题
# Fix EXE Creation Issues

Write-Host "🔧 正在修复EXE创建问题..." -ForegroundColor Cyan

# 检查编译环境
Write-Host "📋 检查编译环境..." -ForegroundColor Yellow

# 1. 检查.NET Framework编译器
$compilers = @(
    "$env:WINDIR\Microsoft.NET\Framework64\v4.0.30319\csc.exe",
    "$env:WINDIR\Microsoft.NET\Framework\v4.0.30319\csc.exe",
    "$env:WINDIR\Microsoft.NET\Framework64\v3.5\csc.exe",
    "$env:WINDIR\Microsoft.NET\Framework\v3.5\csc.exe"
)

$availableCompiler = $null
foreach ($compiler in $compilers) {
    if (Test-Path $compiler) {
        $availableCompiler = $compiler
        Write-Host "✅ 找到编译器: $compiler" -ForegroundColor Green
        break
    }
}

if (-not $availableCompiler) {
    Write-Host "❌ 未找到.NET编译器" -ForegroundColor Red
    Write-Host "正在尝试使用PowerShell转EXE的替代方案..." -ForegroundColor Yellow
    
    # 使用ps2exe模块（如果可用）
    try {
        if (-not (Get-Module -ListAvailable -Name ps2exe)) {
            Write-Host "正在安装ps2exe模块..." -ForegroundColor Cyan
            Install-Module -Name ps2exe -Force -Scope CurrentUser
        }
        
        Import-Module ps2exe
        
        $inputScript = "Augment-VIP-ENTERPRISE-SECURITY\AugmentVIP-LICENSED-SECURE.ps1"
        $outputExe = "Augment-VIP-ENTERPRISE-SECURITY\AugmentVIP-中文安全版-PS2EXE.exe"
        
        Write-Host "使用ps2exe转换PowerShell脚本..." -ForegroundColor Cyan
        ps2exe -inputFile $inputScript -outputFile $outputExe -noConsole:$false -title "Augment VIP 工具 - 中文安全版" -description "Augment VIP Tool Chinese Security Edition" -company "Augment Code" -product "Augment VIP Tool" -copyright "© 2025 Augment Code" -version "*******"
        
        if (Test-Path $outputExe) {
            Write-Host "✅ ps2exe转换成功!" -ForegroundColor Green
            $fileInfo = Get-Item $outputExe
            Write-Host "文件: $($fileInfo.Name)" -ForegroundColor White
            Write-Host "大小: $($fileInfo.Length) bytes" -ForegroundColor White
        }
        
    } catch {
        Write-Host "⚠️ ps2exe方法失败: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# 2. 创建简化的C#版本
Write-Host "📦 创建简化的C#版本..." -ForegroundColor Yellow

$simpleCSharp = @'
using System;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using System.IO;

class Program
{
    private static string expectedHash = "db42154cfde3cabadf3521ef55f88a74e8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8";
    
    static void Main()
    {
        try
        {
            // 设置控制台编码
            Console.OutputEncoding = Encoding.UTF8;
            
            // 显示中文界面
            ShowLicenseInterface();
            
            // 获取用户输入
            string userInput = Console.ReadLine();
            
            Console.WriteLine();
            Console.WriteLine("正在验证许可证...");
            
            if (ValidateLicense(userInput))
            {
                ShowSuccessMessage();
                LogAccess(true, userInput);
                
                // 执行安全检查
                if (PerformSecurityChecks())
                {
                    // 启动PowerShell脚本
                    LaunchPowerShellScript();
                }
            }
            else
            {
                ShowFailureMessage();
                LogAccess(false, userInput);
                
                Console.WriteLine("应用程序将在3秒后退出...");
                System.Threading.Thread.Sleep(3000);
                Environment.Exit(1);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误: {ex.Message}");
            Environment.Exit(1);
        }
    }
    
    static void ShowLicenseInterface()
    {
        Console.WriteLine();
        Console.WriteLine("================================================================");
        Console.WriteLine("                AUGMENT VIP 工具 - 企业安全版                  ");
        Console.WriteLine("================================================================");
        Console.WriteLine("产品名称: Augment VIP 工具企业安全版");
        Console.WriteLine("版本信息: v2.0 中文EXE版");
        Console.WriteLine("保护等级: 需要许可证密钥");
        Console.WriteLine("================================================================");
        Console.WriteLine();
        Console.WriteLine("请输入您的许可证密钥以继续使用:");
        Console.WriteLine("（许可证密钥区分大小写，请准确输入）");
        Console.WriteLine();
        Console.Write("许可证密钥: ");
    }
    
    static void ShowSuccessMessage()
    {
        Console.WriteLine();
        Console.WriteLine("================================================================");
        Console.WriteLine("                      许可证验证成功                           ");
        Console.WriteLine("================================================================");
        Console.WriteLine("状态信息: 许可证密钥有效");
        Console.WriteLine("授权状态: 已获得授权");
        Console.WriteLine($"验证时间: {DateTime.Now:yyyy年MM月dd日 HH:mm:ss}");
        Console.WriteLine($"用户信息: {Environment.UserName}@{Environment.MachineName}");
        Console.WriteLine("================================================================");
        Console.WriteLine();
        Console.WriteLine("正在启动应用程序...");
    }
    
    static void ShowFailureMessage()
    {
        Console.WriteLine();
        Console.WriteLine("================================================================");
        Console.WriteLine("                      许可证验证失败                           ");
        Console.WriteLine("================================================================");
        Console.WriteLine("错误信息: 无效的许可证密钥");
        Console.WriteLine("解决方案: 请联系软件提供商获取有效的许可证");
        Console.WriteLine("技术支持: <EMAIL>");
        Console.WriteLine("警告信息: 未经授权使用本软件是违法行为");
        Console.WriteLine("================================================================");
        Console.WriteLine();
    }
    
    static bool ValidateLicense(string inputKey)
    {
        try
        {
            if (string.IsNullOrEmpty(inputKey))
                return false;
            
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] keyBytes = Encoding.UTF8.GetBytes(inputKey.Trim());
                byte[] hashBytes = sha256.ComputeHash(keyBytes);
                string inputHash = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
                
                return inputHash.Equals(expectedHash, StringComparison.OrdinalIgnoreCase);
            }
        }
        catch
        {
            return false;
        }
    }
    
    static bool PerformSecurityChecks()
    {
        Console.WriteLine("[安全检查] 正在初始化安全检查...");
        
        // 检查调试器
        if (Debugger.IsAttached)
        {
            Console.WriteLine("[安全警告] 检测到调试器附加!");
            Console.WriteLine("[安全警告] 因安全原因，应用程序将被终止。");
            System.Threading.Thread.Sleep(3000);
            Environment.Exit(1);
        }
        
        Console.WriteLine("[安全检查] 安全检查通过。正在启动应用程序...");
        return true;
    }
    
    static void LaunchPowerShellScript()
    {
        try
        {
            ProcessStartInfo psi = new ProcessStartInfo();
            psi.FileName = "powershell.exe";
            psi.Arguments = "-ExecutionPolicy Bypass -File \"AugmentVIP-LICENSED-SECURE.ps1\"";
            psi.UseShellExecute = false;
            psi.CreateNoWindow = false;
            
            Process process = Process.Start(psi);
            if (process != null)
            {
                process.WaitForExit();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"启动PowerShell脚本失败: {ex.Message}");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
    
    static void LogAccess(bool authorized, string input)
    {
        try
        {
            string logDir = "logs";
            if (!Directory.Exists(logDir))
                Directory.CreateDirectory(logDir);
            
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            string logEntry;
            string logFile;
            
            if (authorized)
            {
                logEntry = $"{timestamp} - 授权访问 - 用户: {Environment.UserName} - 计算机: {Environment.MachineName}";
                logFile = Path.Combine(logDir, "license-access.log");
            }
            else
            {
                string inputPreview = input.Length > 10 ? input.Substring(0, 10) + "..." : input;
                logEntry = $"{timestamp} - 未授权访问尝试 - 用户: {Environment.UserName} - 计算机: {Environment.MachineName} - 输入: {inputPreview}";
                logFile = Path.Combine(logDir, "license-violations.log");
            }
            
            File.AppendAllText(logFile, logEntry + Environment.NewLine, Encoding.UTF8);
        }
        catch
        {
            // 忽略日志错误
        }
    }
}
'@

# 保存简化的C#代码
$simpleCSharp | Out-File "SimpleChineseAugmentVIP.cs" -Encoding UTF8

if ($availableCompiler) {
    Write-Host "📦 使用.NET编译器编译..." -ForegroundColor Cyan
    
    $outputFile = "Augment-VIP-ENTERPRISE-SECURITY\AugmentVIP-中文安全版.exe"
    $args = @(
        "/out:$outputFile",
        "/target:exe",
        "/optimize+",
        "SimpleChineseAugmentVIP.cs"
    )
    
    $process = Start-Process -FilePath $availableCompiler -ArgumentList $args -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "✅ 简化版EXE编译成功!" -ForegroundColor Green
        
        $fileInfo = Get-Item $outputFile
        Write-Host ""
        Write-Host "🎯 EXE文件信息:" -ForegroundColor Green
        Write-Host "文件名: $($fileInfo.Name)" -ForegroundColor White
        Write-Host "大小: $($fileInfo.Length) bytes" -ForegroundColor White
        Write-Host "创建时间: $($fileInfo.CreationTime)" -ForegroundColor White
        
        # 尝试签名
        if (Test-Path "AugmentVIP-CodeSigning.pfx") {
            try {
                Write-Host "🔐 正在签名..." -ForegroundColor Yellow
                $cert = Get-PfxCertificate -FilePath "AugmentVIP-CodeSigning.pfx"
                $result = Set-AuthenticodeSignature -FilePath $outputFile -Certificate $cert -TimestampServer "http://timestamp.digicert.com"
                Write-Host "签名状态: $($result.Status)" -ForegroundColor Cyan
            } catch {
                Write-Host "⚠️ 签名失败: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
        
    } else {
        Write-Host "❌ 编译失败，退出代码: $($process.ExitCode)" -ForegroundColor Red
    }
}

# 3. 创建批处理文件作为备选方案
Write-Host "📝 创建批处理文件备选方案..." -ForegroundColor Yellow

$batchContent = @'
@echo off
chcp 65001 >nul
title Augment VIP 工具 - 中文安全版

echo.
echo ================================================================
echo                 AUGMENT VIP 工具 - 企业安全版                  
echo ================================================================
echo 产品名称: Augment VIP 工具企业安全版
echo 版本信息: v2.0 中文批处理版
echo 保护等级: 需要许可证密钥
echo ================================================================
echo.

powershell -ExecutionPolicy Bypass -File "AugmentVIP-LICENSED-SECURE.ps1"

pause
'@

$batchContent | Out-File "Augment-VIP-ENTERPRISE-SECURITY\AugmentVIP-中文安全版.bat" -Encoding UTF8

Write-Host "✅ 批处理文件已创建" -ForegroundColor Green

# 清理临时文件
Remove-Item "SimpleChineseAugmentVIP.cs" -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "🎉 EXE创建完成!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 可用的启动方式:" -ForegroundColor Cyan
Write-Host "1. AugmentVIP-中文安全版.exe (如果编译成功)" -ForegroundColor White
Write-Host "2. AugmentVIP-中文安全版-PS2EXE.exe (如果ps2exe可用)" -ForegroundColor White
Write-Host "3. AugmentVIP-中文安全版.bat (批处理文件)" -ForegroundColor White
Write-Host "4. AugmentVIP-LICENSED-SECURE.ps1 (PowerShell脚本)" -ForegroundColor White
Write-Host ""
Write-Host "💡 推荐使用顺序:" -ForegroundColor Yellow
Write-Host "1. 首选: .exe文件 (独立运行)" -ForegroundColor White
Write-Host "2. 备选: .bat文件 (需要PowerShell)" -ForegroundColor White
Write-Host "3. 直接: .ps1文件 (开发调试)" -ForegroundColor White
