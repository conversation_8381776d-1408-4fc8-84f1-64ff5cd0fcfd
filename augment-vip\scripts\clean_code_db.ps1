# clean_code_db.ps1
#
# Description: Cleans VS Code databases by removing entries containing "augment"
# This script finds the appropriate database files based on the operating system,
# creates backups, and then removes specific records from the SQLite databases.

param(
    [switch]$Help
)

# Show help message
if ($Help) {
    Write-Host "Augment VIP Database Cleaner (PowerShell Version)" -ForegroundColor Green
    Write-Host ""
    Write-Host "Description: Removes Augment-related entries from VS Code databases"
    Write-Host ""
    Write-Host "Usage: .\clean_code_db.ps1"
    Write-Host ""
    Write-Host "Requirements:"
    Write-Host "  - SQLite3 executable (will attempt to download if not found)"
    Write-Host "  - VS Code installed"
    Write-Host ""
    exit 0
}

# Function to write colored output
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "INFO" { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Blue }
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green }
        "WARNING" { Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor Yellow }
        "ERROR" { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red }
    }
}

# Function to check if SQLite3 is available
function Test-SQLite3 {
    try {
        $null = Get-Command sqlite3 -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# Function to download SQLite3 if not available
function Install-SQLite3 {
    Write-Log "SQLite3 not found. Attempting to download..." "WARNING"
    
    $sqliteUrl = "https://www.sqlite.org/2023/sqlite-tools-win32-x86-3420000.zip"
    $tempDir = Join-Path $env:TEMP "sqlite3_temp"
    $zipPath = Join-Path $tempDir "sqlite3.zip"
    $extractPath = Join-Path $tempDir "extracted"
    
    try {
        # Create temp directory
        if (Test-Path $tempDir) {
            Remove-Item $tempDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        New-Item -ItemType Directory -Path $extractPath -Force | Out-Null
        
        # Download SQLite3
        Write-Log "Downloading SQLite3..." "INFO"
        Invoke-WebRequest -Uri $sqliteUrl -OutFile $zipPath
        
        # Extract
        Write-Log "Extracting SQLite3..." "INFO"
        Expand-Archive -Path $zipPath -DestinationPath $extractPath -Force
        
        # Find sqlite3.exe and copy to current directory
        $sqlite3Exe = Get-ChildItem -Path $extractPath -Name "sqlite3.exe" -Recurse | Select-Object -First 1
        if ($sqlite3Exe) {
            $sourcePath = Join-Path $extractPath $sqlite3Exe
            $destPath = Join-Path (Get-Location) "sqlite3.exe"
            Copy-Item $sourcePath $destPath
            Write-Log "SQLite3 downloaded and installed successfully" "SUCCESS"
            return $true
        }
        else {
            Write-Log "Could not find sqlite3.exe in downloaded archive" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Failed to download SQLite3: $($_.Exception.Message)" "ERROR"
        return $false
    }
    finally {
        # Cleanup
        if (Test-Path $tempDir) {
            Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
}

# Function to get VS Code database paths
function Get-VSCodeDatabasePaths {
    $paths = @()
    
    # Windows paths for VS Code
    $appDataPath = $env:APPDATA
    if ($appDataPath) {
        $vscodePath = Join-Path $appDataPath "Code\User\globalStorage\state.vscdb"
        if (Test-Path $vscodePath) {
            $paths += $vscodePath
        }
    }
    
    # Also check for VS Code Insiders
    if ($appDataPath) {
        $vscodeInsidersPath = Join-Path $appDataPath "Code - Insiders\User\globalStorage\state.vscdb"
        if (Test-Path $vscodeInsidersPath) {
            $paths += $vscodeInsidersPath
        }
    }
    
    return $paths
}

# Function to clean a single database
function Clean-Database {
    param(
        [string]$DatabasePath
    )
    
    Write-Log "Processing database: $DatabasePath" "INFO"
    
    # Create backup
    $backupPath = "$DatabasePath.backup"
    try {
        Copy-Item $DatabasePath $backupPath -Force
        Write-Log "Created backup: $backupPath" "SUCCESS"
    }
    catch {
        Write-Log "Failed to create backup: $($_.Exception.Message)" "ERROR"
        return $false
    }
    
    # Clean the database
    try {
        $sqliteCmd = if (Test-Path ".\sqlite3.exe") { ".\sqlite3.exe" } else { "sqlite3" }
        $query = "DELETE FROM ItemTable WHERE key LIKE '%augment%';"
        
        & $sqliteCmd $DatabasePath $query
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Successfully cleaned database" "SUCCESS"
            return $true
        }
        else {
            Write-Log "SQLite command failed with exit code: $LASTEXITCODE" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Failed to clean database: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main execution
function Main {
    Write-Log "Starting VS Code database cleanup process" "INFO"
    
    # Check for SQLite3
    if (-not (Test-SQLite3)) {
        if (-not (Install-SQLite3)) {
            Write-Log "Cannot proceed without SQLite3. Please install it manually." "ERROR"
            exit 1
        }
    }
    
    # Get database paths
    Write-Log "Searching for VS Code database files..." "INFO"
    $dbPaths = Get-VSCodeDatabasePaths
    
    if ($dbPaths.Count -eq 0) {
        Write-Log "No VS Code database files found" "WARNING"
        Write-Log "Make sure VS Code is installed and has been run at least once" "INFO"
        exit 1
    }
    
    Write-Log "Found $($dbPaths.Count) database file(s)" "SUCCESS"
    
    # Process each database
    $successCount = 0
    foreach ($dbPath in $dbPaths) {
        if (Clean-Database $dbPath) {
            $successCount++
        }
    }
    
    # Summary
    if ($successCount -eq $dbPaths.Count) {
        Write-Log "All databases processed successfully" "SUCCESS"
    }
    else {
        Write-Log "$successCount out of $($dbPaths.Count) databases processed successfully" "WARNING"
    }
    
    Write-Log "Database cleanup process completed" "INFO"
}

# Execute main function
Main
