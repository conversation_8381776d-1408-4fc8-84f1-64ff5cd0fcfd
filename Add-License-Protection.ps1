# Add License Protection with Verification Code
# 添加许可证保护和验证码机制

param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    [string]$OutputFile = "",
    [string]$LicenseKey = "AKDJFDHSKOMGRIOINOFWEOIPEWFIOM3289589894393290543"
)

Write-Host "🔐 AUGMENT VIP TOOL - 添加验证码保护" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

if (-not (Test-Path $InputFile)) {
    Write-Host "❌ 输入文件不存在: $InputFile" -ForegroundColor Red
    exit 1
}

if ($OutputFile -eq "") {
    $OutputFile = $InputFile.Replace(".ps1", "-Licensed.ps1")
}

Write-Host "📁 输入文件: $InputFile" -ForegroundColor Green
Write-Host "📁 输出文件: $OutputFile" -ForegroundColor Green
Write-Host "🔑 验证码: $($LicenseKey.Substring(0,10))..." -ForegroundColor Yellow
Write-Host ""

# 读取原始代码
$originalCode = Get-Content $InputFile -Raw

# 生成验证码哈希
$licenseHash = [System.Security.Cryptography.SHA256]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes($LicenseKey))
$licenseHashString = [System.BitConverter]::ToString($licenseHash).Replace("-", "").ToLower()

Write-Host "🔒 生成许可证哈希: $($licenseHashString.Substring(0,16))..." -ForegroundColor Cyan

# 创建许可证保护代码
$licenseProtectionCode = @"
# AUGMENT VIP TOOL - LICENSED VERSION
# 此版本受许可证保护，需要有效的验证码才能运行

# 许可证验证模块
class LicenseManager {
    static [string] `$ExpectedHash = "$licenseHashString"
    static [string] `$ProductName = "Augment VIP Tool Enterprise"
    static [string] `$Version = "v2.0"
    static [bool] `$IsLicensed = `$false
    static [datetime] `$LastCheck = (Get-Date).AddDays(-1)
    
    # 验证许可证密钥
    static [bool] ValidateLicense([string]`$inputKey) {
        try {
            if (-not `$inputKey -or `$inputKey.Length -eq 0) {
                return `$false
            }
            
            # 计算输入密钥的哈希
            `$keyBytes = [System.Text.Encoding]::UTF8.GetBytes(`$inputKey.Trim())
            `$sha256 = [System.Security.Cryptography.SHA256]::Create()
            `$hashBytes = `$sha256.ComputeHash(`$keyBytes)
            `$inputHash = [System.BitConverter]::ToString(`$hashBytes).Replace("-", "").ToLower()
            `$sha256.Dispose()
            
            # 比较哈希值
            if (`$inputHash -eq [LicenseManager]::`$ExpectedHash) {
                [LicenseManager]::`$IsLicensed = `$true
                [LicenseManager]::`$LastCheck = Get-Date
                return `$true
            }
            
            return `$false
        } catch {
            return `$false
        }
    }
    
    # 显示许可证信息
    static [void] ShowLicenseInfo() {
        Write-Host ""
        Write-Host "╔══════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
        Write-Host "║                    许可证保护系统                        ║" -ForegroundColor Cyan
        Write-Host "╠══════════════════════════════════════════════════════════╣" -ForegroundColor Cyan
        Write-Host "║ 产品名称: [LicenseManager]::`$ProductName                ║" -ForegroundColor White
        Write-Host "║ 版本信息: [LicenseManager]::`$Version                    ║" -ForegroundColor White
        Write-Host "║ 保护等级: 企业级安全                                    ║" -ForegroundColor White
        Write-Host "║ 状态信息: 需要有效验证码                                ║" -ForegroundColor Yellow
        Write-Host "╚══════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
        Write-Host ""
    }
    
    # 许可证验证失败处理
    static [void] HandleLicenseFailure() {
        Write-Host ""
        Write-Host "╔══════════════════════════════════════════════════════════╗" -ForegroundColor Red
        Write-Host "║                    许可证验证失败                        ║" -ForegroundColor Red
        Write-Host "╠══════════════════════════════════════════════════════════╣" -ForegroundColor Red
        Write-Host "║ 错误信息: 无效的验证码                                  ║" -ForegroundColor White
        Write-Host "║ 解决方案: 请联系软件提供商获取有效的验证码              ║" -ForegroundColor White
        Write-Host "║ 技术支持: <EMAIL>                       ║" -ForegroundColor White
        Write-Host "║ 警告信息: 未经授权使用本软件是违法行为                  ║" -ForegroundColor Yellow
        Write-Host "╚══════════════════════════════════════════════════════════╝" -ForegroundColor Red
        Write-Host ""
        
        # 记录未授权访问尝试
        try {
            `$logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - 未授权访问尝试 - 用户: `$env:USERNAME - 计算机: `$env:COMPUTERNAME"
            `$logDir = "logs"
            if (-not (Test-Path `$logDir)) {
                New-Item -ItemType Directory -Path `$logDir -Force | Out-Null
            }
            Add-Content -Path "`$logDir\license-violations.log" -Value `$logEntry -Encoding UTF8
        } catch {
            # 忽略日志错误
        }
        
        Write-Host "程序将在3秒后退出..." -ForegroundColor Red
        Start-Sleep 3
        exit 1
    }
    
    # 许可证验证成功处理
    static [void] HandleLicenseSuccess() {
        Write-Host ""
        Write-Host "╔══════════════════════════════════════════════════════════╗" -ForegroundColor Green
        Write-Host "║                    许可证验证成功                        ║" -ForegroundColor Green
        Write-Host "╠══════════════════════════════════════════════════════════╣" -ForegroundColor Green
        Write-Host "║ 状态信息: 验证码有效                                    ║" -ForegroundColor White
        Write-Host "║ 授权状态: 已授权使用                                    ║" -ForegroundColor White
        Write-Host "║ 验证时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')     ║" -ForegroundColor White
        Write-Host "║ 用户信息: `$env:USERNAME@`$env:COMPUTERNAME              ║" -ForegroundColor White
        Write-Host "╚══════════════════════════════════════════════════════════╝" -ForegroundColor Green
        Write-Host ""
        
        # 记录合法访问
        try {
            `$logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - 合法访问 - 用户: `$env:USERNAME - 计算机: `$env:COMPUTERNAME"
            `$logDir = "logs"
            if (-not (Test-Path `$logDir)) {
                New-Item -ItemType Directory -Path `$logDir -Force | Out-Null
            }
            Add-Content -Path "`$logDir\license-access.log" -Value `$logEntry -Encoding UTF8
        } catch {
            # 忽略日志错误
        }
        
        Write-Host "正在启动应用程序..." -ForegroundColor Green
        Start-Sleep 2
    }
    
    # 运行时许可证检查
    static [bool] RuntimeLicenseCheck() {
        # 每30分钟检查一次许可证状态
        `$timeSinceLastCheck = (Get-Date) - [LicenseManager]::`$LastCheck
        if (`$timeSinceLastCheck.TotalMinutes -gt 30) {
            if (-not [LicenseManager]::`$IsLicensed) {
                Write-Host "[LICENSE] 运行时许可证检查失败" -ForegroundColor Red
                return `$false
            }
            [LicenseManager]::`$LastCheck = Get-Date
        }
        return `$true
    }
}

# 显示许可证信息
[LicenseManager]::ShowLicenseInfo()

# 提示用户输入验证码
Write-Host "请输入验证码以继续使用本软件:" -ForegroundColor Yellow
Write-Host "(验证码区分大小写，请准确输入)" -ForegroundColor Gray
Write-Host ""

# 获取用户输入
`$userInput = Read-Host "验证码"

# 验证许可证
Write-Host ""
Write-Host "正在验证许可证..." -ForegroundColor Yellow

if ([LicenseManager]::ValidateLicense(`$userInput)) {
    [LicenseManager]::HandleLicenseSuccess()
} else {
    [LicenseManager]::HandleLicenseFailure()
}

# 启动运行时监控
`$licenseMonitor = Start-Job -ScriptBlock {
    while (`$true) {
        Start-Sleep 1800  # 30分钟检查一次
        if (-not [LicenseManager]::RuntimeLicenseCheck()) {
            Write-Host "[LICENSE] 运行时许可证验证失败，程序将退出" -ForegroundColor Red
            Stop-Process -Name "powershell" -Force -ErrorAction SilentlyContinue
            break
        }
    }
}

Write-Host "[LICENSE] 许可证监控已启动" -ForegroundColor Green

# 执行原始代码
try {
$originalCode
} finally {
    # 清理许可证监控
    if (`$licenseMonitor) {
        Stop-Job `$licenseMonitor -ErrorAction SilentlyContinue
        Remove-Job `$licenseMonitor -ErrorAction SilentlyContinue
    }
    Write-Host "[LICENSE] 程序已安全退出" -ForegroundColor Green
}
"@

# 保存带许可证保护的代码
$licenseProtectionCode | Out-File $OutputFile -Encoding UTF8

Write-Host "✅ 验证码保护已添加!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 保护功能:" -ForegroundColor Cyan
Write-Host "  ✓ 启动时验证码验证" -ForegroundColor Green
Write-Host "  ✓ 运行时许可证监控" -ForegroundColor Green
Write-Host "  ✓ 未授权访问记录" -ForegroundColor Green
Write-Host "  ✓ 合法访问日志" -ForegroundColor Green
Write-Host "  ✓ 自动许可证检查" -ForegroundColor Green
Write-Host ""
Write-Host "🔑 验证码信息:" -ForegroundColor Yellow
Write-Host "  验证码: $LicenseKey" -ForegroundColor White
Write-Host "  哈希值: $($licenseHashString.Substring(0,32))..." -ForegroundColor Gray
Write-Host ""
Write-Host "📁 输出文件: $OutputFile" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 使用说明:" -ForegroundColor Yellow
Write-Host "  1. 运行程序时会提示输入验证码" -ForegroundColor White
Write-Host "  2. 输入正确的验证码后程序才能启动" -ForegroundColor White
Write-Host "  3. 程序运行期间会定期检查许可证状态" -ForegroundColor White
Write-Host "  4. 所有访问尝试都会被记录到日志文件" -ForegroundColor White
