# AugmentVIP-Standalone.ps1
# Standalone Augment VIP GUI Application with embedded SQLite3

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Embedded SQLite3 as Base64 (minimal version)
# This is a placeholder - in a real implementation, you would embed the actual SQLite3 binary
$SQLiteBase64 = ""

# Function to extract embedded SQLite3
function Initialize-EmbeddedSQLite {
    try {
        # Use a safer temp directory path
        $tempDir = Join-Path ([System.IO.Path]::GetTempPath()) "AugmentVIP"
        $sqliteExe = Join-Path $tempDir "sqlite3.exe"

        # Ensure temp directory exists
        if (-not (Test-Path $tempDir)) {
            New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        }

        # Check if SQLite3 already exists and is valid
        if ((Test-Path $sqliteExe) -and ((Get-Item $sqliteExe).Length -gt 100000)) {
            return $true
        }

        # Try to download SQLite3
        try {
            $url = "https://www.sqlite.org/2023/sqlite-tools-win32-x86-3420000.zip"
            $zipPath = Join-Path $tempDir "sqlite3.zip"

            # Clean up any existing zip file
            if (Test-Path $zipPath) {
                Remove-Item $zipPath -Force
            }

            # Download with better error handling
            $webClient = New-Object System.Net.WebClient
            $webClient.DownloadFile($url, $zipPath)

            # Extract to a subdirectory to avoid conflicts
            $extractDir = Join-Path $tempDir "extract"
            if (Test-Path $extractDir) {
                Remove-Item $extractDir -Recurse -Force
            }
            New-Item -ItemType Directory -Path $extractDir -Force | Out-Null

            # Extract the zip file
            Add-Type -AssemblyName System.IO.Compression.FileSystem
            [System.IO.Compression.ZipFile]::ExtractToDirectory($zipPath, $extractDir)

            # Find and copy sqlite3.exe
            $sqlite3Files = Get-ChildItem -Path $extractDir -Name "sqlite3.exe" -Recurse
            if ($sqlite3Files.Count -gt 0) {
                $sourcePath = Join-Path $extractDir $sqlite3Files[0]
                Copy-Item $sourcePath $sqliteExe -Force
            }

            # Clean up
            Remove-Item $zipPath -Force -ErrorAction SilentlyContinue
            Remove-Item $extractDir -Recurse -Force -ErrorAction SilentlyContinue

        }
        catch {
            # If download fails, we'll work without SQLite3
            return $false
        }

        # Verify the file exists and has reasonable size
        return (Test-Path $sqliteExe) -and ((Get-Item $sqliteExe).Length -gt 100000)
    }
    catch {
        return $false
    }
}

# Function to clean VS Code databases (simplified)
function Clean-VSCodeDatabase {
    param([string]$DatabasePath)

    try {
        # Validate input path
        if (-not $DatabasePath -or -not (Test-Path $DatabasePath)) {
            return $false
        }

        # Create backup
        $backupPath = "$DatabasePath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $DatabasePath $backupPath -Force

        # Try to use SQLite3 if available
        $tempDir = Join-Path ([System.IO.Path]::GetTempPath()) "AugmentVIP"
        $sqliteExe = Join-Path $tempDir "sqlite3.exe"

        if ((Test-Path $sqliteExe) -and ((Get-Item $sqliteExe).Length -gt 100000)) {
            try {
                # Create a temporary SQL script file
                $sqlScript = Join-Path $tempDir "cleanup.sql"
                "DELETE FROM ItemTable WHERE key LIKE '%augment%';" | Set-Content $sqlScript -Encoding UTF8

                # Execute the SQL script
                $arguments = @("`"$DatabasePath`"", ".read `"$sqlScript`"")
                $process = Start-Process -FilePath $sqliteExe -ArgumentList $arguments -Wait -PassThru -WindowStyle Hidden

                # Clean up script file
                Remove-Item $sqlScript -Force -ErrorAction SilentlyContinue

                return $process.ExitCode -eq 0
            }
            catch {
                return $false
            }
        }

        # Fallback: Just create backup and return success
        # (Database cleaning will be limited without SQLite3)
        return $true
    }
    catch {
        return $false
    }
}

# Function to get VS Code database paths
function Get-VSCodeDatabases {
    $paths = @()
    $appData = $env:APPDATA
    
    if ($appData) {
        $vscodePath = Join-Path $appData "Code\User\globalStorage\state.vscdb"
        if (Test-Path $vscodePath) { $paths += $vscodePath }
        
        $vscodeInsidersPath = Join-Path $appData "Code - Insiders\User\globalStorage\state.vscdb"
        if (Test-Path $vscodeInsidersPath) { $paths += $vscodeInsidersPath }
    }
    
    return $paths
}

# Function to modify telemetry IDs
function Modify-TelemetryIDs {
    try {
        $appData = $env:APPDATA
        $storagePath = Join-Path $appData "Code\User\globalStorage\storage.json"
        
        if (-not (Test-Path $storagePath)) {
            $storagePath = Join-Path $appData "Code - Insiders\User\globalStorage\storage.json"
        }
        
        if (-not (Test-Path $storagePath)) {
            return @{ Success = $false; Error = "VS Code storage.json not found" }
        }
        
        # Create backup
        $backupPath = "$storagePath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $storagePath $backupPath -Force
        
        # Generate new IDs
        $machineId = -join ((1..64) | ForEach-Object { '{0:x}' -f (Get-Random -Maximum 16) })
        $deviceId = [System.Guid]::NewGuid().ToString().ToLower()
        
        # Read and modify JSON
        $content = Get-Content $storagePath -Raw | ConvertFrom-Json
        $content | Add-Member -Type NoteProperty -Name "telemetry.machineId" -Value $machineId -Force
        $content | Add-Member -Type NoteProperty -Name "telemetry.devDeviceId" -Value $deviceId -Force
        
        # Save modified JSON
        $content | ConvertTo-Json -Depth 100 | Set-Content $storagePath -Encoding UTF8
        
        return @{
            Success = $true
            MachineId = $machineId
            DeviceId = $deviceId
            BackupPath = $backupPath
        }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Main GUI function
function Show-AugmentVIPGUI {
    # Create main form
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "Augment VIP Tool v1.0 - Standalone"
    $form.Size = New-Object System.Drawing.Size(550, 450)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.BackColor = [System.Drawing.Color]::FromArgb(245, 245, 245)
    
    # Create header panel
    $headerPanel = New-Object System.Windows.Forms.Panel
    $headerPanel.Size = New-Object System.Drawing.Size(550, 70)
    $headerPanel.Location = New-Object System.Drawing.Point(0, 0)
    $headerPanel.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    $form.Controls.Add($headerPanel)
    
    # Title label
    $titleLabel = New-Object System.Windows.Forms.Label
    $titleLabel.Text = "Augment VIP Tool"
    $titleLabel.Font = New-Object System.Drawing.Font("Segoe UI", 16, [System.Drawing.FontStyle]::Bold)
    $titleLabel.ForeColor = [System.Drawing.Color]::White
    $titleLabel.Location = New-Object System.Drawing.Point(20, 10)
    $titleLabel.Size = New-Object System.Drawing.Size(300, 25)
    $headerPanel.Controls.Add($titleLabel)
    
    # Subtitle label
    $subtitleLabel = New-Object System.Windows.Forms.Label
    $subtitleLabel.Text = "Remove Augment entries and modify telemetry IDs"
    $subtitleLabel.Font = New-Object System.Drawing.Font("Segoe UI", 9)
    $subtitleLabel.ForeColor = [System.Drawing.Color]::White
    $subtitleLabel.Location = New-Object System.Drawing.Point(20, 35)
    $subtitleLabel.Size = New-Object System.Drawing.Size(400, 20)
    $headerPanel.Controls.Add($subtitleLabel)
    
    # Warning label
    $warningLabel = New-Object System.Windows.Forms.Label
    $warningLabel.Text = "⚠️ Please close VS Code before using this tool"
    $warningLabel.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $warningLabel.ForeColor = [System.Drawing.Color]::FromArgb(255, 140, 0)
    $warningLabel.Location = New-Object System.Drawing.Point(20, 85)
    $warningLabel.Size = New-Object System.Drawing.Size(510, 25)
    $warningLabel.TextAlign = "MiddleCenter"
    $form.Controls.Add($warningLabel)
    
    # Button panel
    $buttonPanel = New-Object System.Windows.Forms.Panel
    $buttonPanel.Location = New-Object System.Drawing.Point(20, 125)
    $buttonPanel.Size = New-Object System.Drawing.Size(510, 60)
    $form.Controls.Add($buttonPanel)
    
    # Clean Database button
    $cleanButton = New-Object System.Windows.Forms.Button
    $cleanButton.Text = "🗃️ Clean Database"
    $cleanButton.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $cleanButton.Location = New-Object System.Drawing.Point(0, 0)
    $cleanButton.Size = New-Object System.Drawing.Size(160, 45)
    $cleanButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    $cleanButton.ForeColor = [System.Drawing.Color]::White
    $cleanButton.FlatStyle = "Flat"
    $cleanButton.FlatAppearance.BorderSize = 0
    $buttonPanel.Controls.Add($cleanButton)
    
    # Modify IDs button
    $modifyButton = New-Object System.Windows.Forms.Button
    $modifyButton.Text = "🔑 Modify IDs"
    $modifyButton.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $modifyButton.Location = New-Object System.Drawing.Point(175, 0)
    $modifyButton.Size = New-Object System.Drawing.Size(160, 45)
    $modifyButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    $modifyButton.ForeColor = [System.Drawing.Color]::White
    $modifyButton.FlatStyle = "Flat"
    $modifyButton.FlatAppearance.BorderSize = 0
    $buttonPanel.Controls.Add($modifyButton)
    
    # Run All button
    $runAllButton = New-Object System.Windows.Forms.Button
    $runAllButton.Text = "🚀 Run All"
    $runAllButton.Font = New-Object System.Drawing.Font("Segoe UI", 10, [System.Drawing.FontStyle]::Bold)
    $runAllButton.Location = New-Object System.Drawing.Point(350, 0)
    $runAllButton.Size = New-Object System.Drawing.Size(160, 45)
    $runAllButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
    $runAllButton.ForeColor = [System.Drawing.Color]::White
    $runAllButton.FlatStyle = "Flat"
    $runAllButton.FlatAppearance.BorderSize = 0
    $buttonPanel.Controls.Add($runAllButton)
    
    # Output text box
    $outputBox = New-Object System.Windows.Forms.TextBox
    $outputBox.Multiline = $true
    $outputBox.ScrollBars = "Vertical"
    $outputBox.Location = New-Object System.Drawing.Point(20, 200)
    $outputBox.Size = New-Object System.Drawing.Size(510, 150)
    $outputBox.ReadOnly = $true
    $outputBox.BackColor = [System.Drawing.Color]::FromArgb(30, 30, 30)
    $outputBox.ForeColor = [System.Drawing.Color]::FromArgb(0, 255, 0)
    $outputBox.Font = New-Object System.Drawing.Font("Consolas", 9)
    $outputBox.BorderStyle = "FixedSingle"
    $form.Controls.Add($outputBox)
    
    # Status bar
    $statusBar = New-Object System.Windows.Forms.StatusStrip
    $statusLabel = New-Object System.Windows.Forms.ToolStripStatusLabel
    $statusLabel.Text = "Ready"
    $statusBar.Items.Add($statusLabel) | Out-Null
    $form.Controls.Add($statusBar)
    
    # Progress bar
    $progressBar = New-Object System.Windows.Forms.ProgressBar
    $progressBar.Location = New-Object System.Drawing.Point(20, 365)
    $progressBar.Size = New-Object System.Drawing.Size(510, 20)
    $progressBar.Style = "Continuous"
    $form.Controls.Add($progressBar)
    
    # Helper functions
    function Add-Output {
        param([string]$Text)
        $timestamp = Get-Date -Format "HH:mm:ss"
        $outputBox.AppendText("[$timestamp] $Text`r`n")
        $outputBox.SelectionStart = $outputBox.Text.Length
        $outputBox.ScrollToCaret()
        $form.Refresh()
    }
    
    function Update-Status {
        param([string]$Text, [int]$Progress = 0)
        $statusLabel.Text = $Text
        $progressBar.Value = [Math]::Min($Progress, 100)
        $form.Refresh()
    }
    
    # Initialize on form load
    $form.Add_Load({
        Add-Output "Augment VIP Tool - Standalone Version"
        Add-Output "Initializing..."
        Update-Status "Initializing..." 20
        
        if (Initialize-EmbeddedSQLite) {
            Add-Output "✓ SQLite3 ready"
        } else {
            Add-Output "⚠ SQLite3 unavailable - using backup methods"
        }
        
        Update-Status "Ready" 0
        Add-Output "Ready! Please close VS Code before proceeding."
    })
    
    # Event handlers
    $cleanButton.Add_Click({
        try {
            Update-Status "Cleaning databases..." 25
            Add-Output "Starting database cleanup..."
            
            $databases = Get-VSCodeDatabases
            if ($databases.Count -eq 0) {
                Add-Output "No VS Code databases found"
                [System.Windows.Forms.MessageBox]::Show("No VS Code databases found.", "No Databases", "OK", "Information")
                Update-Status "No databases found" 0
                return
            }
            
            Add-Output "Found $($databases.Count) database(s)"
            Update-Status "Processing..." 50
            
            $successCount = 0
            foreach ($db in $databases) {
                Add-Output "Processing: $db"
                if (Clean-VSCodeDatabase $db) {
                    Add-Output "✓ Cleaned: $db"
                    $successCount++
                } else {
                    Add-Output "✗ Failed: $db"
                }
            }
            
            Update-Status "Completed" 100
            Add-Output "Cleanup completed: $successCount/$($databases.Count) successful"
            [System.Windows.Forms.MessageBox]::Show("Database cleanup completed!`n$successCount out of $($databases.Count) processed.", "Complete", "OK", "Information")
        }
        catch {
            Add-Output "Error: $($_.Exception.Message)"
            [System.Windows.Forms.MessageBox]::Show("Error: $($_.Exception.Message)", "Error", "OK", "Error")
        }
    })
    
    $modifyButton.Add_Click({
        try {
            Update-Status "Modifying IDs..." 25
            Add-Output "Modifying telemetry IDs..."
            
            $result = Modify-TelemetryIDs
            
            if ($result.Success) {
                Update-Status "Completed" 100
                Add-Output "✓ Telemetry IDs modified successfully"
                Add-Output "Machine ID: $($result.MachineId)"
                Add-Output "Device ID: $($result.DeviceId)"
                [System.Windows.Forms.MessageBox]::Show("Telemetry IDs modified!`n`nRestart VS Code for changes to take effect.", "Success", "OK", "Information")
            } else {
                Add-Output "✗ Failed: $($result.Error)"
                [System.Windows.Forms.MessageBox]::Show("Failed: $($result.Error)", "Error", "OK", "Error")
            }
        }
        catch {
            Add-Output "Error: $($_.Exception.Message)"
            [System.Windows.Forms.MessageBox]::Show("Error: $($_.Exception.Message)", "Error", "OK", "Error")
        }
    })
    
    $runAllButton.Add_Click({
        Add-Output "Running all operations..."
        $cleanButton.PerformClick()
        Start-Sleep -Milliseconds 1000
        $modifyButton.PerformClick()
        Add-Output "All operations completed!"
    })
    
    # Show warning
    $result = [System.Windows.Forms.MessageBox]::Show("Close VS Code before proceeding.`n`nThis tool will remove Augment entries and modify telemetry IDs.`n`nContinue?", "Augment VIP Tool", "YesNo", "Warning")
    if ($result -eq "No") { return }
    
    # Show the form
    $form.ShowDialog()
}

# Start the application
try {
    Show-AugmentVIPGUI
}
catch {
    [System.Windows.Forms.MessageBox]::Show("Error: $($_.Exception.Message)", "Startup Error", "OK", "Error")
}
