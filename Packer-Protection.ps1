# 加壳保护工具
# Packer Protection Tool

param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    [string]$OutputFile = "",
    [string]$PackerType = "UPX"
)

Write-Host "📦 AUGMENT VIP TOOL - 加壳保护工具" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

if (-not (Test-Path $InputFile)) {
    Write-Host "❌ 输入文件不存在: $InputFile" -ForegroundColor Red
    exit 1
}

if ($OutputFile -eq "") {
    $OutputFile = $InputFile.Replace(".exe", "-Packed.exe")
}

Write-Host "📁 输入文件: $InputFile" -ForegroundColor Green
Write-Host "📁 输出文件: $OutputFile" -ForegroundColor Green
Write-Host "📦 加壳类型: $PackerType" -ForegroundColor Green
Write-Host ""

# UPX加壳函数
function Use-UPXPacker {
    param([string]$Input, [string]$Output)
    
    Write-Host "📦 使用UPX加壳..." -ForegroundColor Yellow
    
    # 检查UPX是否可用
    $upxPath = "upx.exe"
    if (-not (Get-Command $upxPath -ErrorAction SilentlyContinue)) {
        Write-Host "⚠️ UPX未找到，尝试下载..." -ForegroundColor Yellow
        
        # 下载UPX（简化版本）
        try {
            $upxUrl = "https://github.com/upx/upx/releases/download/v4.0.2/upx-4.0.2-win64.zip"
            Write-Host "正在下载UPX..." -ForegroundColor Cyan
            # 这里应该实现下载逻辑，但为了安全起见，我们提供手动下载指导
            Write-Host "请手动下载UPX并放置在当前目录:" -ForegroundColor Yellow
            Write-Host "下载地址: $upxUrl" -ForegroundColor White
            return $false
        } catch {
            Write-Host "❌ UPX下载失败" -ForegroundColor Red
            return $false
        }
    }
    
    try {
        # 复制文件到输出位置
        Copy-Item $Input $Output -Force
        
        # 执行UPX压缩
        $upxArgs = @("--best", "--compress-exports=1", "--compress-icons=1", $Output)
        $process = Start-Process -FilePath $upxPath -ArgumentList $upxArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Host "✅ UPX加壳成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ UPX加壳失败，退出代码: $($process.ExitCode)" -ForegroundColor Red
            return $false
        }
        
    } catch {
        Write-Host "❌ UPX加壳过程出错: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 自定义简单加壳函数
function Use-CustomPacker {
    param([string]$Input, [string]$Output)
    
    Write-Host "🔧 使用自定义加壳..." -ForegroundColor Yellow
    
    try {
        # 读取原始文件
        $originalBytes = [System.IO.File]::ReadAllBytes($Input)
        Write-Host "原始文件大小: $($originalBytes.Length) bytes" -ForegroundColor Cyan
        
        # 简单的XOR加密
        $key = 0xAB
        $encryptedBytes = @()
        for ($i = 0; $i -lt $originalBytes.Length; $i++) {
            $encryptedBytes += $originalBytes[$i] -bxor $key
        }
        
        # 创建解包器代码
        $unpackerCode = @"
using System;
using System.IO;
using System.Reflection;
using System.Diagnostics;

namespace AugmentVIPPacker
{
    class Program
    {
        static void Main(string[] args)
        {
            // Anti-debugging check
            if (Debugger.IsAttached)
            {
                Console.WriteLine("Debugging detected. Exiting...");
                Environment.Exit(1);
            }
            
            // Decrypt and load original executable
            byte[] encryptedData = {$($encryptedBytes -join ',')};
            byte[] decryptedData = new byte[encryptedData.Length];
            
            for (int i = 0; i < encryptedData.Length; i++)
            {
                decryptedData[i] = (byte)(encryptedData[i] ^ 0xAB);
            }
            
            // Write to temp file and execute
            string tempFile = Path.GetTempFileName() + ".exe";
            File.WriteAllBytes(tempFile, decryptedData);
            
            try
            {
                Process.Start(tempFile);
            }
            finally
            {
                // Clean up temp file after a delay
                System.Threading.Thread.Sleep(1000);
                try { File.Delete(tempFile); } catch { }
            }
        }
    }
}
"@
        
        # 保存解包器代码
        $unpackerFile = [System.IO.Path]::GetTempFileName() + ".cs"
        $unpackerCode | Out-File $unpackerFile -Encoding UTF8
        
        # 编译解包器
        $compilerPath = "$env:WINDIR\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
        if (-not (Test-Path $compilerPath)) {
            $compilerPath = "$env:WINDIR\Microsoft.NET\Framework\v4.0.30319\csc.exe"
        }
        
        if (Test-Path $compilerPath) {
            $compileArgs = @("/out:$Output", "/target:exe", "/optimize+", $unpackerFile)
            $process = Start-Process -FilePath $compilerPath -ArgumentList $compileArgs -Wait -PassThru -NoNewWindow
            
            if ($process.ExitCode -eq 0) {
                Write-Host "✅ 自定义加壳成功" -ForegroundColor Green
                
                # 清理临时文件
                Remove-Item $unpackerFile -Force -ErrorAction SilentlyContinue
                
                return $true
            } else {
                Write-Host "❌ 编译失败，退出代码: $($process.ExitCode)" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "❌ 未找到.NET编译器" -ForegroundColor Red
            return $false
        }
        
    } catch {
        Write-Host "❌ 自定义加壳失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# PowerShell脚本加壳函数
function Pack-PowerShellScript {
    param([string]$Input, [string]$Output)
    
    Write-Host "📜 PowerShell脚本加壳..." -ForegroundColor Yellow
    
    try {
        # 读取原始脚本
        $originalScript = Get-Content $Input -Raw
        
        # 压缩脚本
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($originalScript)
        $compressedBytes = [System.IO.Compression.GzipStream]::new([System.IO.MemoryStream]::new(), [System.IO.Compression.CompressionMode]::Compress)
        $compressedBytes.Write($bytes, 0, $bytes.Length)
        $compressedBytes.Close()
        $compressedData = $compressedBytes.BaseStream.ToArray()
        $base64Compressed = [System.Convert]::ToBase64String($compressedData)
        
        # 创建加壳后的脚本
        $packedScript = @"
# Augment VIP Tool - Packed PowerShell Script
# Anti-tampering and compression protection

# Anti-debugging check
if ([System.Diagnostics.Debugger]::IsAttached) {
    Write-Host "Debugging detected. Exiting..." -ForegroundColor Red
    exit 1
}

# Environment check
if (`$env:USERNAME -eq "SYSTEM" -or `$env:USERNAME -eq "Administrator") {
    Write-Host "Running in suspicious environment. Exiting..." -ForegroundColor Red
    exit 1
}

# Decompress and execute original script
try {
    `$compressedData = [System.Convert]::FromBase64String('$base64Compressed')
    `$memoryStream = New-Object System.IO.MemoryStream(,`$compressedData)
    `$gzipStream = New-Object System.IO.Compression.GzipStream(`$memoryStream, [System.IO.Compression.CompressionMode]::Decompress)
    `$streamReader = New-Object System.IO.StreamReader(`$gzipStream)
    `$decompressedScript = `$streamReader.ReadToEnd()
    `$streamReader.Close()
    `$gzipStream.Close()
    `$memoryStream.Close()
    
    # Execute original script
    Invoke-Expression `$decompressedScript
    
} catch {
    Write-Host "Failed to decompress script: `$(`$_.Exception.Message)" -ForegroundColor Red
    exit 1
}
"@
        
        # 保存加壳后的脚本
        $packedScript | Out-File $Output -Encoding UTF8
        
        Write-Host "✅ PowerShell脚本加壳成功" -ForegroundColor Green
        Write-Host "   压缩比: $([math]::Round((1 - $base64Compressed.Length / $originalScript.Length) * 100, 2))%" -ForegroundColor Cyan
        
        return $true
        
    } catch {
        Write-Host "❌ PowerShell脚本加壳失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 主执行逻辑
Write-Host "开始加壳保护..." -ForegroundColor Green
Write-Host ""

$success = $false

# 根据文件类型选择加壳方法
$fileExtension = [System.IO.Path]::GetExtension($InputFile).ToLower()

switch ($fileExtension) {
    ".exe" {
        switch ($PackerType.ToUpper()) {
            "UPX" {
                $success = Use-UPXPacker -Input $InputFile -Output $OutputFile
            }
            "CUSTOM" {
                $success = Use-CustomPacker -Input $InputFile -Output $OutputFile
            }
            default {
                Write-Host "⚠️ 未知的加壳类型，使用自定义加壳" -ForegroundColor Yellow
                $success = Use-CustomPacker -Input $InputFile -Output $OutputFile
            }
        }
    }
    ".ps1" {
        $success = Pack-PowerShellScript -Input $InputFile -Output $OutputFile
    }
    default {
        Write-Host "❌ 不支持的文件类型: $fileExtension" -ForegroundColor Red
        exit 1
    }
}

# 显示结果
Write-Host ""
if ($success) {
    Write-Host "🎉 加壳保护完成!" -ForegroundColor Green
    
    # 显示文件大小对比
    $originalSize = (Get-Item $InputFile).Length
    $packedSize = (Get-Item $OutputFile).Length
    $compressionRatio = [math]::Round((1 - $packedSize / $originalSize) * 100, 2)
    
    Write-Host "📊 统计信息:" -ForegroundColor Cyan
    Write-Host "   原始大小: $originalSize bytes" -ForegroundColor White
    Write-Host "   加壳大小: $packedSize bytes" -ForegroundColor White
    Write-Host "   压缩比: $compressionRatio%" -ForegroundColor White
    
} else {
    Write-Host "❌ 加壳保护失败" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "💡 使用方法:" -ForegroundColor Cyan
Write-Host "   .\Packer-Protection.ps1 -InputFile 'file.exe'" -ForegroundColor White
Write-Host "   .\Packer-Protection.ps1 -InputFile 'file.exe' -PackerType 'UPX'" -ForegroundColor White
Write-Host "   .\Packer-Protection.ps1 -InputFile 'script.ps1'" -ForegroundColor White
