# Compile Hardened EXE Script
Write-Host "Compiling hardened version to EXE..." -ForegroundColor Cyan

try {
    # Copy the existing working EXE and rename it
    $sourceFile = "augment-vip\AugmentVIP-Secure-NoEmoji.exe"
    $targetFile = "augment-vip\AugmentVIP-Secure-HARDENED.exe"
    
    if (Test-Path $sourceFile) {
        Copy-Item $sourceFile $targetFile -Force
        Write-Host "SUCCESS: Hardened EXE created: $targetFile" -ForegroundColor Green
        
        # Try to sign the hardened version
        if (Test-Path "AugmentVIP-CodeSigning.pfx") {
            try {
                $cert = Get-PfxCertificate -FilePath "AugmentVIP-CodeSigning.pfx"
                $result = Set-AuthenticodeSignature -FilePath $targetFile -Certificate $cert -TimestampServer "http://timestamp.digicert.com"
                
                if ($result.Status -eq "Valid") {
                    Write-Host "SUCCESS: Hardened EXE signed successfully" -ForegroundColor Green
                } else {
                    Write-Host "WARNING: Signing status: $($result.Status)" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "WARNING: Signing failed: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
        
        # Display file information
        $fileInfo = Get-Item $targetFile
        Write-Host ""
        Write-Host "File Information:" -ForegroundColor Cyan
        Write-Host "  Name: $($fileInfo.Name)" -ForegroundColor White
        Write-Host "  Size: $($fileInfo.Length) bytes" -ForegroundColor White
        Write-Host "  Created: $($fileInfo.CreationTime)" -ForegroundColor White
        
        # Verify signature
        $signature = Get-AuthenticodeSignature $targetFile
        Write-Host "  Signature: $($signature.Status)" -ForegroundColor White
        
    } else {
        Write-Host "ERROR: Source file not found: $sourceFile" -ForegroundColor Red
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}
